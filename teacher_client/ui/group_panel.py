#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教师端小组管理面板
"""

import os
import sys
import requests
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTreeWidget,
    QTreeWidgetItem, QHeaderView, QMenu, QAction, QMessageBox, QGroupBox,
    QComboBox, QLineEdit, QFormLayout, QInputDialog, QDialog, QListWidget,
    QListWidgetItem, QDialogButtonBox, QGridLayout
)
from PyQt5.QtCore import Qt, QSize, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QIcon, QColor, QBrush

class GroupPanel(QWidget):
    """小组管理面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.groups = {}  # 存储小组信息
        self.students = {}  # 存储学生信息
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 创建主布局
        layout = QVBoxLayout(self)
        
        # 创建操作区域
        action_layout = QHBoxLayout()
        
        # 添加刷新按钮
        refresh_btn = QPushButton("刷新小组")
        refresh_btn.clicked.connect(self.refresh)
        action_layout.addWidget(refresh_btn)
        
        # 添加创建小组按钮
        create_btn = QPushButton("创建小组")
        create_btn.clicked.connect(self.create_group)
        action_layout.addWidget(create_btn)
        
        # 添加随机分组按钮
        random_btn = QPushButton("随机分组")
        random_btn.clicked.connect(self.random_assign)
        action_layout.addWidget(random_btn)
        
        # 添加操作区域到主布局
        layout.addLayout(action_layout)
        
        # 创建小组过滤区域
        filter_group = QGroupBox("小组过滤")
        filter_layout = QFormLayout()
        
        # 小组名称搜索
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入小组名称搜索...")
        self.search_input.textChanged.connect(self.apply_filter)
        filter_layout.addRow("搜索:", self.search_input)
        
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)
        
        # 创建小组树形视图
        self.group_tree = QTreeWidget()
        self.group_tree.setHeaderLabels(["小组名称", "成员数", "设备ID", "状态"])
        self.group_tree.setAlternatingRowColors(True)
        self.group_tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.group_tree.customContextMenuRequested.connect(self.show_context_menu)
        self.group_tree.itemExpanded.connect(self.on_item_expanded)
        
        # 设置列宽
        header = self.group_tree.header()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        layout.addWidget(self.group_tree)
        
        # 创建状态标签
        self.status_label = QLabel("小组总数: 0 | 学生总数: 0")
        layout.addWidget(self.status_label)
        
        # 初始加载小组
        self.refresh()
    
    def load_groups(self, classroom_id):
        """加载指定课堂的小组列表"""
        try:
            if not classroom_id:
                self.parent.status_bar.showMessage("无效的课堂ID", 3000)
                return
            
            # 打印调试信息
            print(f"加载课堂 {classroom_id} 的小组")
            
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 发送请求获取小组列表
            response = requests.get(f"{api_base_url}/groups?classroom_id={classroom_id}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    groups = data.get('groups', [])
                    print(f"获取到 {len(groups)} 个小组")
                    self.update_groups(groups)
                else:
                    self.parent.show_error(f"获取小组列表失败: {data.get('message')}")
            else:
                self.parent.show_error(f"获取小组列表失败: HTTP {response.status_code}")
            
            # 获取学生列表
            response = requests.get(f"{api_base_url}/groups/students?classroom_id={classroom_id}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    students = data.get('students', [])
                    print(f"获取到 {len(students)} 个学生")
                    self.update_students(students)
                else:
                    self.parent.show_error(f"获取学生列表失败: {data.get('message')}")
            else:
                self.parent.show_error(f"获取学生列表失败: HTTP {response.status_code}")
            
            self.parent.status_bar.showMessage(f"已加载课堂 {classroom_id} 的小组", 3000)
        
        except Exception as e:
            self.parent.show_error(f"加载小组列表时出错: {str(e)}")
    
    def refresh(self):
        """刷新小组列表"""
        try:
            # 获取当前课堂ID
            classroom_id = self.get_current_classroom_id()
            if not classroom_id:
                self.parent.status_bar.showMessage("请先选择课堂", 3000)
                return
            
            # 加载指定课堂的小组
            self.load_groups(classroom_id)
        
        except Exception as e:
            self.parent.show_error(f"刷新小组列表时出错: {str(e)}")
    
    def get_current_classroom_id(self):
        """获取当前选中的课堂ID"""
        # 从主窗口的课堂选择下拉框获取当前课堂ID
        combo = self.parent.classroom_combo
        classroom_id = combo.currentData() if combo.currentIndex() >= 0 else None
        
        # 打印调试信息
        if classroom_id:
            print(f"当前选中的课堂ID: {classroom_id}")
        else:
            print("未选择课堂")
            
        return classroom_id
    
    def update_groups(self, groups):
        """更新小组列表"""
        # 清空小组树
        self.group_tree.clear()
        self.groups = {}
        
        # 添加小组到树形视图
        for group in groups:
            self.add_group(group)
        
        # 更新状态标签
        self.update_status_label()
        
        # 应用过滤器
        self.apply_filter()
    
    def update_students(self, students):
        """更新学生列表"""
        self.students = {student['id']: student for student in students}
        
        # 更新小组成员
        for group_id, group in self.groups.items():
            # 查找属于该小组的学生
            group_students = [s for s in students if s.get('group_id') == group_id]
            group['members'] = group_students
            
            # 更新小组项的成员数
            for i in range(self.group_tree.topLevelItemCount()):
                item = self.group_tree.topLevelItem(i)
                if item.data(0, Qt.UserRole) == group_id:
                    item.setText(1, str(len(group_students)))
                    
                    # 如果项已展开，更新成员列表
                    if item.isExpanded():
                        self.update_group_members(item, group_students)
                    
                    break
        
        # 更新状态标签
        self.update_status_label()
    
    def add_group(self, group):
        """添加小组到树形视图"""
        group_id = group.get('id')
        group_name = group.get('name', '未命名小组')
        device_id = group.get('device_id', '无')
        
        # 获取小组成员数量
        members_count = 0
        
        # 创建树形项
        item = QTreeWidgetItem([group_name, str(members_count), device_id, ''])
        
        # 设置小组ID为用户数据
        item.setData(0, Qt.UserRole, group_id)
        
        # 添加到树形视图
        self.group_tree.addTopLevelItem(item)
        
        # 存储小组信息
        self.groups[group_id] = group
    
    def update_group(self, group):
        """更新单个小组信息"""
        group_id = group.get('id')
        
        # 更新存储的小组信息
        self.groups[group_id] = group
        
        # 查找并更新树形项
        for i in range(self.group_tree.topLevelItemCount()):
            item = self.group_tree.topLevelItem(i)
            if item.data(0, Qt.UserRole) == group_id:
                item.setText(0, group.get('name', '未命名小组'))
                item.setText(2, group.get('device_id', '无'))
                
                # 如果项已展开，更新成员列表
                if item.isExpanded():
                    self.update_group_members(item, group.get('members', []))
                
                break
        
        # 更新状态标签
        self.update_status_label()
        
        # 应用过滤器
        self.apply_filter()
    
    def update_group_members(self, group_item, members):
        """更新小组成员列表"""
        # 清空现有成员
        while group_item.childCount() > 0:
            group_item.removeChild(group_item.child(0))
        
        # 添加成员
        for member in members:
            student_id = member.get('id')
            student_name = member.get('name', '未知学生')
            student_number = member.get('student_number', '无')
            
            # 创建成员项
            member_item = QTreeWidgetItem([student_name, '', student_number, ''])
            
            # 设置学生ID为用户数据
            member_item.setData(0, Qt.UserRole, student_id)
            
            # 添加到小组项
            group_item.addChild(member_item)
    
    def on_item_expanded(self, item):
        """处理项展开事件"""
        # 检查是否为小组项
        if item.parent() is None:
            group_id = item.data(0, Qt.UserRole)
            group = self.groups.get(group_id, {})
            
            # 获取小组成员
            members = group.get('members', [])
            
            # 更新成员列表
            self.update_group_members(item, members)
    
    def update_status_label(self):
        """更新状态标签"""
        group_count = len(self.groups)
        student_count = len(self.students)
        
        self.status_label.setText(f"小组总数: {group_count} | 学生总数: {student_count}")
    
    def apply_filter(self):
        """应用过滤器"""
        search_text = self.search_input.text().lower()
        
        # 遍历所有小组项
        for i in range(self.group_tree.topLevelItemCount()):
            item = self.group_tree.topLevelItem(i)
            group_id = item.data(0, Qt.UserRole)
            group = self.groups.get(group_id, {})
            
            # 检查是否符合过滤条件
            name_match = not search_text or search_text in group.get('name', '').lower()
            
            # 设置项的可见性
            item.setHidden(not name_match)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.group_tree.itemAt(position)
        if not item:
            return
        
        # 检查是否为小组项或学生项
        if item.parent() is None:
            # 小组项
            group_id = item.data(0, Qt.UserRole)
            self.show_group_menu(position, group_id)
        else:
            # 学生项
            student_id = item.data(0, Qt.UserRole)
            group_id = item.parent().data(0, Qt.UserRole)
            self.show_student_menu(position, student_id, group_id)
    
    def show_group_menu(self, position, group_id):
        """显示小组右键菜单"""
        group = self.groups.get(group_id, {})
        
        # 创建菜单
        menu = QMenu()
        
        # 添加查看详情选项
        view_action = QAction("查看详情", self)
        view_action.triggered.connect(lambda: self.view_group_details(group_id))
        menu.addAction(view_action)
        
        # 添加管理成员选项
        manage_action = QAction("管理成员", self)
        manage_action.triggered.connect(lambda: self.manage_group_members(group_id))
        menu.addAction(manage_action)
        
        # 添加分隔符
        menu.addSeparator()
        
        # 添加重命名选项
        rename_action = QAction("重命名", self)
        rename_action.triggered.connect(lambda: self.rename_group(group_id))
        menu.addAction(rename_action)
        
        # 添加删除选项
        delete_action = QAction("删除小组", self)
        delete_action.triggered.connect(lambda: self.delete_group(group_id))
        menu.addAction(delete_action)
        
        # 显示菜单
        menu.exec_(self.group_tree.viewport().mapToGlobal(position))
    
    def show_student_menu(self, position, student_id, group_id):
        """显示学生右键菜单"""
        student = self.students.get(student_id, {})
        
        # 创建菜单
        menu = QMenu()
        
        # 添加查看详情选项
        view_action = QAction("查看详情", self)
        view_action.triggered.connect(lambda: self.view_student_details(student_id))
        menu.addAction(view_action)
        
        # 添加移出小组选项
        remove_action = QAction("移出小组", self)
        remove_action.triggered.connect(lambda: self.remove_student_from_group(student_id, group_id))
        menu.addAction(remove_action)
        
        # 显示菜单
        menu.exec_(self.group_tree.viewport().mapToGlobal(position))
    
    def view_group_details(self, group_id):
        """查看小组详情"""
        group = self.groups.get(group_id, {})
        
        # 构建详情消息
        details = f"小组ID: {group_id}\n"
        details += f"小组名称: {group.get('name', '未知')}\n"
        details += f"设备ID: {group.get('device_id', '无')}\n"
        
        members = group.get('members', [])
        details += f"成员数量: {len(members)}\n"
        
        if members:
            details += "\n成员列表:\n"
            for member in members:
                details += f"- {member.get('name', '未知')} ({member.get('student_number', '无')})\n"
        
        # 显示详情对话框
        QMessageBox.information(self, "小组详情", details)
    
    def view_student_details(self, student_id):
        """查看学生详情"""
        student = self.students.get(student_id, {})
        
        # 构建详情消息
        details = f"学生ID: {student_id}\n"
        details += f"姓名: {student.get('name', '未知')}\n"
        details += f"学号: {student.get('student_number', '无')}\n"
        
        group_id = student.get('group_id')
        if group_id and group_id in self.groups:
            details += f"所属小组: {self.groups[group_id].get('name', '未知')}\n"
        else:
            details += "所属小组: 无\n"
        
        # 显示详情对话框
        QMessageBox.information(self, "学生详情", details)
    
    def manage_group_members(self, group_id):
        """管理小组成员"""
        group = self.groups.get(group_id, {})
        
        # 创建对话框
        dialog = QDialog(self)
        dialog.setWindowTitle(f"管理小组成员 - {group.get('name', '未知小组')}")
        dialog.setMinimumWidth(400)
        
        # 创建布局
        layout = QVBoxLayout(dialog)
        
        # 创建学生列表
        student_list = QListWidget()
        
        # 获取当前课堂的所有学生
        classroom_id = self.get_current_classroom_id()
        all_students = [s for s in self.students.values() if s.get('classroom_id') == classroom_id]
        
        # 添加学生到列表
        for student in all_students:
            item = QListWidgetItem(f"{student.get('name', '未知')} ({student.get('student_number', '无')})")
            item.setData(Qt.UserRole, student.get('id'))
            
            # 如果学生已在小组中，则选中
            if student.get('group_id') == group_id:
                item.setCheckState(Qt.Checked)
            else:
                item.setCheckState(Qt.Unchecked)
            
            student_list.addItem(item)
        
        layout.addWidget(QLabel("选择小组成员:"))
        layout.addWidget(student_list)
        
        # 添加按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)
        
        # 显示对话框
        if dialog.exec_() == QDialog.Accepted:
            # 处理选择结果
            selected_students = []
            unselected_students = []
            
            for i in range(student_list.count()):
                item = student_list.item(i)
                student_id = item.data(Qt.UserRole)
                
                if item.checkState() == Qt.Checked:
                    selected_students.append(student_id)
                else:
                    # 如果学生原本在小组中，则需要移除
                    student = self.students.get(student_id, {})
                    if student.get('group_id') == group_id:
                        unselected_students.append(student_id)
            
            # 更新小组成员
            self.update_group_members_api(group_id, selected_students, unselected_students)
    
    def update_group_members_api(self, group_id, add_students, remove_students):
        """调用API更新小组成员"""
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 添加学生到小组
            for student_id in add_students:
                # 检查学生是否已在小组中
                student = self.students.get(student_id, {})
                if student.get('group_id') != group_id:
                    response = requests.post(
                        f"{api_base_url}/groups/{group_id}/members",
                        json={"student_id": student_id}
                    )
                    
                    if response.status_code != 200:
                        self.parent.show_error(f"添加学生到小组失败: HTTP {response.status_code}")
            
            # 从小组中移除学生
            for student_id in remove_students:
                response = requests.delete(
                    f"{api_base_url}/groups/{group_id}/members/{student_id}"
                )
                
                if response.status_code != 200:
                    self.parent.show_error(f"从小组中移除学生失败: HTTP {response.status_code}")
            
            # 刷新小组列表
            self.refresh()
            
            self.parent.status_bar.showMessage("小组成员已更新", 3000)
        
        except Exception as e:
            self.parent.show_error(f"更新小组成员时出错: {str(e)}")
    
    def remove_student_from_group(self, student_id, group_id):
        """从小组中移除学生"""
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 发送请求移除学生
            response = requests.delete(
                f"{api_base_url}/groups/{group_id}/members/{student_id}"
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.parent.status_bar.showMessage("已从小组中移除学生", 3000)
                    # 更新学生信息
                    self.refresh()
                else:
                    self.parent.show_error(f"从小组中移除学生失败: {data.get('message')}")
            else:
                self.parent.show_error(f"从小组中移除学生失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.parent.show_error(f"从小组中移除学生时出错: {str(e)}")
    
    def create_group(self):
        """创建新小组"""
        # 获取当前课堂ID
        classroom_id = self.get_current_classroom_id()
        if not classroom_id:
            self.parent.status_bar.showMessage("请先选择课堂", 3000)
            return
        
        # 显示创建小组对话框
        name, ok = QInputDialog.getText(
            self, "创建小组", "请输入小组名称:"
        )
        
        if ok and name:
            try:
                # 获取API基础URL
                api_base_url = self.parent.api_base_url
                
                # 打印调试信息
                print(f"创建小组 - 课堂ID: {classroom_id}, 小组名称: {name}")
                
                # 发送请求创建小组
                response = requests.post(
                    f"{api_base_url}/groups",
                    json={
                        "classroom_id": classroom_id,
                        "name": name
                    }
                )
                
                # 打印响应信息
                print(f"创建小组响应: {response.status_code}")
                if response.text:
                    print(f"响应内容: {response.text}")
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        self.parent.status_bar.showMessage("小组创建成功", 3000)
                        # 更新小组列表
                        self.refresh()
                    else:
                        self.parent.show_error(f"创建小组失败: {data.get('message')}")
                else:
                    self.parent.show_error(f"创建小组失败: HTTP {response.status_code}")
            
            except Exception as e:
                self.parent.show_error(f"创建小组时出错: {str(e)}")
    
    def rename_group(self, group_id):
        """重命名小组"""
        group = self.groups.get(group_id, {})
        current_name = group.get('name', '')
        
        # 显示重命名对话框
        new_name, ok = QInputDialog.getText(
            self, "重命名小组", "请输入新的小组名称:", 
            QLineEdit.Normal, current_name
        )
        
        if ok and new_name:
            try:
                # 获取API基础URL
                api_base_url = self.parent.api_base_url
                
                # 发送请求重命名小组
                response = requests.put(
                    f"{api_base_url}/groups/{group_id}",
                    json={"name": new_name}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        self.parent.status_bar.showMessage("小组已重命名", 3000)
                        # 更新小组信息
                        self.refresh()
                    else:
                        self.parent.show_error(f"重命名小组失败: {data.get('message')}")
                else:
                    self.parent.show_error(f"重命名小组失败: HTTP {response.status_code}")
            
            except Exception as e:
                self.parent.show_error(f"重命名小组时出错: {str(e)}")
    
    def delete_group(self, group_id):
        """删除小组"""
        # 显示确认对话框
        reply = QMessageBox.question(
            self, "删除小组", 
            "确定要删除此小组吗？小组成员将被移出小组。",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # 获取API基础URL
                api_base_url = self.parent.api_base_url
                
                # 发送请求删除小组
                response = requests.delete(f"{api_base_url}/groups/{group_id}")
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        self.parent.status_bar.showMessage("小组已删除", 3000)
                        # 更新小组列表
                        self.refresh()
                    else:
                        self.parent.show_error(f"删除小组失败: {data.get('message')}")
                else:
                    self.parent.show_error(f"删除小组失败: HTTP {response.status_code}")
            
            except Exception as e:
                self.parent.show_error(f"删除小组时出错: {str(e)}")
    
    def random_assign(self):
        """随机分组"""
        # 获取当前课堂ID
        classroom_id = self.get_current_classroom_id()
        if not classroom_id:
            self.parent.status_bar.showMessage("请先选择课堂", 3000)
            return
        
        # 显示随机分组对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("随机分组")
        dialog.setMinimumWidth(300)
        
        # 创建布局
        layout = QFormLayout(dialog)
        
        # 添加分组数量输入框
        group_count = QLineEdit()
        group_count.setText("4")  # 默认4个小组
        layout.addRow("小组数量:", group_count)
        
        # 添加按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)
        
        # 显示对话框
        if dialog.exec_() == QDialog.Accepted:
            try:
                # 获取分组数量
                count = int(group_count.text())
                if count <= 0:
                    self.parent.show_error("小组数量必须大于0")
                    return
                
                # 获取API基础URL
                api_base_url = self.parent.api_base_url
                
                # 发送请求进行随机分组
                response = requests.post(
                    f"{api_base_url}/groups/random-assign",
                    json={
                        "classroom_id": classroom_id,
                        "group_count": count
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        self.parent.status_bar.showMessage("随机分组成功", 3000)
                        # 更新小组列表
                        self.refresh()
                    else:
                        self.parent.show_error(f"随机分组失败: {data.get('message')}")
                else:
                    self.parent.show_error(f"随机分组失败: HTTP {response.status_code}")
            
            except ValueError:
                self.parent.show_error("请输入有效的小组数量")
            except Exception as e:
                self.parent.show_error(f"随机分组时出错: {str(e)}")
    
    def get_group_count(self):
        """获取小组数量"""
        return len(self.groups)