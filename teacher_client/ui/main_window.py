#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教师端主窗口
"""

import os
import sys
import socketio
import requests
import json
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTabWidget, QSplitter, QTreeWidget, QTreeWidgetItem, QStatusBar,
    QMessageBox, QMenu, QAction, QToolBar, QComboBox, QLineEdit, QGridLayout,
    QGroupBox, QScrollArea, QFrame, QSizePolicy, QApplication, QTextEdit
)
from PyQt5.QtCore import Qt, QSize, QTimer, QThread, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QIcon, QPixmap, QFont

# 导入自定义模块
from teacher_client.ui.device_panel import DevicePanel
from teacher_client.ui.group_panel import GroupPanel
from teacher_client.ui.video_panel import VideoPanel
from teacher_client.ui.control_panel import ControlPanel
from teacher_client.ui.enhanced_control_panel import EnhancedControlPanel
from teacher_client.ui.enhanced_video_panel import EnhancedVideoPanel

# 导入配置
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from shared.config import get_config
from shared.ui_themes import theme_manager
from shared.ui_shortcuts import TeacherShortcuts

# 获取配置
config = get_config()

class MainWindow(QMainWindow):
    """教师端主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.config = config
        self.socket = None
        self.api_base_url = f"http://{self.config.BACKEND_HOST}:{self.config.BACKEND_PORT}/api"
        
        # 初始化主题和快捷键
        self.setup_theme_and_shortcuts()
        
        # 初始化UI
        self.init_ui()
        
        # 连接WebSocket
        self.connect_websocket()
        
        # 加载课堂列表
        self.load_classrooms()
        
        # 启动定时器，定期更新设备状态
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(5000)  # 每5秒更新一次
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("智慧课堂系统 - 教师端")
        self.setGeometry(100, 100, 1280, 800)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建状态栏（必须在面板创建之前，因为面板可能会在初始化时访问status_bar）
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 创建工具栏
        self.create_toolbar()

        # 创建主分割器
        main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(main_splitter)

        # 创建左侧面板
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)

        # 创建标签页
        self.tab_widget = QTabWidget()
        left_layout.addWidget(self.tab_widget)

        # 创建设备管理面板
        self.device_panel = DevicePanel(self)
        self.tab_widget.addTab(self.device_panel, "设备管理")

        # 创建小组管理面板
        self.group_panel = GroupPanel(self)
        self.tab_widget.addTab(self.group_panel, "小组管理")

        # 创建控制面板
        self.control_panel = ControlPanel(self)
        self.tab_widget.addTab(self.control_panel, "控制面板")

        # 添加左侧面板到分割器
        main_splitter.addWidget(left_panel)

        # 创建右侧视频面板
        self.video_panel = VideoPanel(self)
        main_splitter.addWidget(self.video_panel)

        # 设置分割器比例
        main_splitter.setSizes([400, 880])
        
        # 添加状态标签
        self.connection_status = QLabel("未连接")
        self.status_bar.addPermanentWidget(self.connection_status)
        
        self.device_count = QLabel("设备: 0")
        self.status_bar.addPermanentWidget(self.device_count)
        
        self.group_count = QLabel("小组: 0")
        self.status_bar.addPermanentWidget(self.group_count)
        
        self.stream_count = QLabel("视频流: 0")
        self.status_bar.addPermanentWidget(self.stream_count)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QToolBar("主工具栏")
        toolbar.setIconSize(QSize(32, 32))
        self.addToolBar(toolbar)
        
        # 创建课堂选择下拉框
        classroom_label = QLabel("当前课堂:")
        toolbar.addWidget(classroom_label)
        
        self.classroom_combo = QComboBox()
        self.classroom_combo.setMinimumWidth(200)
        self.classroom_combo.currentIndexChanged.connect(self.on_classroom_changed)
        toolbar.addWidget(self.classroom_combo)
        
        # 添加创建课堂按钮
        create_classroom_action = QAction("创建课堂", self)
        create_classroom_action.triggered.connect(self.create_new_classroom)
        toolbar.addAction(create_classroom_action)
        
        # 添加刷新按钮
        refresh_action = QAction("刷新", self)
        refresh_action.triggered.connect(self.refresh_all)
        toolbar.addAction(refresh_action)
        
        # 添加分隔符
        toolbar.addSeparator()
        
        # 添加广播按钮
        broadcast_action = QAction("屏幕广播", self)
        broadcast_action.triggered.connect(self.start_broadcast)
        toolbar.addAction(broadcast_action)
        
        # 添加停止广播按钮
        stop_broadcast_action = QAction("停止广播", self)
        stop_broadcast_action.triggered.connect(self.stop_broadcast)
        toolbar.addAction(stop_broadcast_action)
        
        # 添加分隔符
        toolbar.addSeparator()
        
        # 添加设置按钮
        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self.show_settings)
        toolbar.addAction(settings_action)
    
    def connect_websocket(self):
        """连接WebSocket服务器"""
        try:
            self.socket = socketio.Client()
            
            # 注册事件处理函数
            self.socket.on('connect', self.on_connect)
            self.socket.on('disconnect', self.on_disconnect)
            self.socket.on('device_update', self.on_device_update)
            self.socket.on('group_update', self.on_group_update)
            self.socket.on('stream_started', self.on_stream_started)
            self.socket.on('stream_stopped', self.on_stream_stopped)
            
            # 连接到服务器
            self.socket.connect(f"http://{self.config.BACKEND_HOST}:{self.config.BACKEND_PORT}")
            
        except Exception as e:
            self.show_error(f"连接WebSocket服务器失败: {str(e)}")
            self.connection_status.setText("连接失败")
    
    def on_connect(self):
        """WebSocket连接成功处理"""
        self.connection_status.setText("已连接")
        self.status_bar.showMessage("已连接到服务器", 3000)
    
    def on_disconnect(self):
        """WebSocket断开连接处理"""
        self.connection_status.setText("未连接")
        self.status_bar.showMessage("与服务器断开连接", 3000)
    
    def on_device_update(self, data):
        """设备更新事件处理"""
        self.device_panel.update_device(data)
        self.update_status()
    
    def on_group_update(self, data):
        """小组更新事件处理"""
        self.group_panel.update_group(data)
        self.update_status()
    
    def on_stream_started(self, data):
        """流开始事件处理"""
        self.video_panel.add_stream(data)
        self.update_status()
    
    def on_stream_stopped(self, data):
        """流停止事件处理"""
        self.video_panel.remove_stream(data['stream_id'])
        self.update_status()
    
    def update_status(self):
        """更新状态信息"""
        try:
            # 更新设备数量
            device_count = self.device_panel.get_device_count()
            self.device_count.setText(f"设备: {device_count}")
            
            # 更新小组数量
            group_count = self.group_panel.get_group_count()
            self.group_count.setText(f"小组: {group_count}")
            
            # 更新流数量
            stream_count = self.video_panel.get_stream_count()
            self.stream_count.setText(f"视频流: {stream_count}")
            
        except Exception as e:
            self.show_error(f"更新状态信息失败: {str(e)}")
    
    def load_classrooms(self):
        """加载课堂列表"""
        try:
            # 获取教师ID（在实际应用中，这应该从登录信息中获取）
            teacher_id = "teacher_001"  # 示例教师ID
            
            # 获取所有课堂列表
            response = requests.get(f"{self.api_base_url}/classroom/list")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    classrooms = data.get('classrooms', [])
                    
                    # 打印调试信息
                    print(f"加载到 {len(classrooms)} 个课堂:")
                    for classroom in classrooms:
                        print(f"  - ID: {classroom.get('id')}, 名称: {classroom.get('name')}, 状态: {classroom.get('status')}")
                    
                    # 清空下拉框
                    self.classroom_combo.clear()
                    
                    # 添加"请选择课堂"选项
                    self.classroom_combo.addItem("请选择课堂", None)
                    
                    # 添加课堂到下拉框
                    for classroom in classrooms:
                        self.classroom_combo.addItem(
                            classroom.get('name', '未命名课堂'), 
                            classroom.get('classroom_id')  # 使用classroom_id而不是id
                        )
                    
                    # 如果有课堂，选择第一个
                    if classrooms:
                        self.classroom_combo.setCurrentIndex(1)
                    
                    self.status_bar.showMessage(f"已加载 {len(classrooms)} 个课堂", 3000)
                else:
                    self.show_error(f"获取课堂列表失败: {data.get('message')}")
            else:
                self.show_error(f"获取课堂列表失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.show_error(f"加载课堂列表时出错: {str(e)}")
    
    def create_new_classroom(self):
        """创建新课堂"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QFormLayout, QDialogButtonBox
        
        # 创建对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("创建新课堂")
        dialog.setMinimumWidth(400)
        
        # 创建布局
        layout = QVBoxLayout(dialog)
        form_layout = QFormLayout()
        
        # 添加表单字段
        name_input = QLineEdit()
        form_layout.addRow("课堂名称:", name_input)
        
        description_input = QTextEdit()
        description_input.setMaximumHeight(100)
        form_layout.addRow("课堂描述:", description_input)
        
        duration_input = QLineEdit()
        duration_input.setText("120")  # 默认120分钟
        form_layout.addRow("课堂时长(分钟):", duration_input)
        
        layout.addLayout(form_layout)
        
        # 添加按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)
        
        # 显示对话框
        if dialog.exec_() == QDialog.Accepted:
            # 获取表单数据
            name = name_input.text()
            description = description_input.toPlainText()
            duration = duration_input.text()
            
            if not name:
                self.show_error("课堂名称不能为空")
                return
            
            try:
                # 获取教师ID（在实际应用中，这应该从登录信息中获取）
                teacher_id = "teacher_001"  # 示例教师ID
                teacher_name = "示例教师"  # 示例教师名称
                
                # 发送创建课堂请求
                response = requests.post(
                    f"{self.api_base_url}/classroom/create",
                    json={
                        "teacher_id": teacher_id,
                        "teacher_name": teacher_name,
                        "name": name,
                        "description": description,
                        "duration": int(duration) if duration else None
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        self.status_bar.showMessage("课堂创建成功", 3000)
                        
                        # 重新加载课堂列表
                        self.load_classrooms()
                        
                        # 选择新创建的课堂
                        new_classroom = data.get('data', {})
                        new_classroom_id = new_classroom.get('classroom_id')
                        if new_classroom_id:
                            print(f"新创建的课堂ID: {new_classroom_id}")
                            index = self.classroom_combo.findData(new_classroom_id)
                            if index >= 0:
                                self.classroom_combo.setCurrentIndex(index)
                            else:
                                print(f"在下拉框中未找到课堂ID: {new_classroom_id}")
                                # 如果找不到，可能需要重新加载课堂列表
                                self.load_classrooms()
                    else:
                        self.show_error(f"创建课堂失败: {data.get('message')}")
                else:
                    self.show_error(f"创建课堂失败: HTTP {response.status_code}")
            
            except Exception as e:
                self.show_error(f"创建课堂时出错: {str(e)}")
    
    def on_classroom_changed(self, index):
        """课堂选择变更处理"""
        classroom_id = self.classroom_combo.currentData()
        if classroom_id:
            self.status_bar.showMessage(f"已选择课堂: {self.classroom_combo.currentText()}", 3000)
            
            # 更新小组面板
            if hasattr(self.group_panel, 'load_groups'):
                self.group_panel.load_groups(classroom_id)
        
    def refresh_all(self):
        """刷新所有数据"""
        # 重新加载课堂列表
        self.load_classrooms()
        
        # 刷新各面板
        self.device_panel.refresh()
        self.group_panel.refresh()
        self.video_panel.refresh()
        self.update_status()
        self.status_bar.showMessage("数据已刷新", 3000)
    
    def start_broadcast(self):
        """开始屏幕广播"""
        self.video_panel.start_broadcast()
    
    def stop_broadcast(self):
        """停止屏幕广播"""
        self.video_panel.stop_broadcast()
    
    def show_settings(self):
        """显示设置对话框"""
        QMessageBox.information(self, "设置", "设置功能尚未实现")
    
    def show_error(self, message):
        """显示错误消息"""
        QMessageBox.critical(self, "错误", message)
    
    def setup_theme_and_shortcuts(self):
        """设置主题和快捷键"""
        # 应用主题
        app = QApplication.instance()
        if app:
            theme_manager.apply_theme_to_app(app)
        
        # 设置快捷键
        self.shortcut_manager = TeacherShortcuts.setup_shortcuts(self)
    
    # 快捷键回调方法
    def on_refresh_all(self):
        """刷新所有数据"""
        self.refresh_all()
    
    def on_save_session(self):
        """保存会话"""
        QMessageBox.information(self, "保存", "会话保存功能尚未实现")
    
    def on_new_session(self):
        """新建会话"""
        QMessageBox.information(self, "新建", "新建会话功能尚未实现")
    
    def on_open_settings(self):
        """打开设置"""
        self.show_settings()
    
    def on_toggle_fullscreen(self):
        """切换全屏"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
    
    def on_quit_app(self):
        """退出应用"""
        self.close()
    
    def on_refresh_devices(self):
        """刷新设备"""
        if hasattr(self.device_panel, 'refresh'):
            self.device_panel.refresh()
    
    def on_connect_all_devices(self):
        """连接所有设备"""
        QMessageBox.information(self, "连接", "连接所有设备功能尚未实现")
    
    def on_disconnect_all_devices(self):
        """断开所有设备"""
        QMessageBox.information(self, "断开", "断开所有设备功能尚未实现")
    
    def on_random_grouping(self):
        """随机分组"""
        if hasattr(self.group_panel, 'random_grouping'):
            self.group_panel.random_grouping()
    
    def on_clear_groups(self):
        """清除分组"""
        if hasattr(self.group_panel, 'clear_groups'):
            self.group_panel.clear_groups()
    
    def on_next_group(self):
        """下一个小组"""
        if hasattr(self.group_panel, 'select_next_group'):
            self.group_panel.select_next_group()
    
    def on_prev_group(self):
        """上一个小组"""
        if hasattr(self.group_panel, 'select_prev_group'):
            self.group_panel.select_prev_group()
    
    def on_start_broadcast(self):
        """开始广播"""
        self.start_broadcast()
    
    def on_stop_broadcast(self):
        """停止广播"""
        self.stop_broadcast()
    
    def on_toggle_video_panel(self):
        """切换视频面板"""
        if hasattr(self.video_panel, 'toggle_visibility'):
            self.video_panel.toggle_visibility()
    
    def on_switch_video_layout(self):
        """切换视频布局"""
        if hasattr(self.video_panel, 'switch_layout'):
            self.video_panel.switch_layout()
    
    def on_start_quiz(self):
        """开始答题"""
        # 切换到控制面板的答题选项卡
        if hasattr(self.control_panel, 'switch_to_quiz'):
            self.control_panel.switch_to_quiz()
    
    def on_end_quiz(self):
        """结束答题"""
        if hasattr(self.control_panel, 'end_quiz'):
            self.control_panel.end_quiz()
    
    def on_show_results(self):
        """显示结果"""
        if hasattr(self.control_panel, 'show_results'):
            self.control_panel.show_results()
    
    def on_export_results(self):
        """导出结果"""
        QMessageBox.information(self, "导出", "导出结果功能尚未实现")
    
    def on_open_file(self):
        """打开文件"""
        QMessageBox.information(self, "打开", "打开文件功能尚未实现")
    
    def on_distribute_file(self):
        """分发文件"""
        QMessageBox.information(self, "分发", "分发文件功能尚未实现")
    
    def on_show_file_manager(self):
        """显示文件管理器"""
        QMessageBox.information(self, "文件管理", "文件管理器功能尚未实现")
    
    def on_toggle_whiteboard(self):
        """切换白板"""
        QMessageBox.information(self, "白板", "白板功能尚未实现")
    
    def on_clear_whiteboard(self):
        """清除白板"""
        QMessageBox.information(self, "清除", "清除白板功能尚未实现")
    
    def on_save_whiteboard(self):
        """保存白板"""
        QMessageBox.information(self, "保存", "保存白板功能尚未实现")
    
    def on_toggle_display_mode(self):
        """切换显示模式"""
        if hasattr(self.video_panel, 'toggle_display_mode'):
            self.video_panel.toggle_display_mode()
    
    def on_start_timer(self):
        """开始计时"""
        QMessageBox.information(self, "计时", "计时功能尚未实现")
    
    def on_stop_timer(self):
        """停止计时"""
        QMessageBox.information(self, "停止计时", "停止计时功能尚未实现")
    
    def closeEvent(self, event):
        """窗口关闭事件处理"""
        # 断开WebSocket连接
        if self.socket and self.socket.connected:
            self.socket.disconnect()
        
        # 停止定时器
        self.update_timer.stop()
        
        event.accept()