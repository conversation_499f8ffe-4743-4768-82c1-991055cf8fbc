# 智慧课堂系统设计文档

## 概述

智慧课堂系统是一个基于统信UOS的多端协作教学平台，采用分布式架构设计，支持教师端、小组端和学生端的实时交互。系统通过局域网实现设备间的高效通信，提供屏幕共享、协同批注、互动答题等核心功能。

## 架构

### 系统架构图

```mermaid
graph TB
    subgraph "教师端 (PyQt5)"
        TA[教师应用]
        TC[控制面板]
        TV[视频显示]
        TW[白板工具]
    end
    
    subgraph "后端服务 (Flask)"
        API[REST API]
        WS[WebSocket服务]
        DB[(SQLite数据库)]
        FS[文件服务]
    end
    
    subgraph "视频服务"
        MTX[MediaMTX服务器]
        FF[FFmpeg处理]
    end
    
    subgraph "小组端 (PyQt5)"
        GA[小组应用]
        GV[视频播放]
        GW[协同白板]
        GC[投屏控制]
    end
    
    subgraph "学生端 (Web)"
        SW[Web界面]
        SC[课堂互动]
        SM[移动适配]
    end
    
    subgraph "网络层"
        UDP[UDP广播]
        TCP[TCP连接]
        HTTP[HTTP/WebSocket]
    end
    
    TA --> API
    TA --> WS
    TA --> MTX
    GA --> API
    GA --> WS
    GA --> MTX
    SW --> API
    SW --> WS
    
    API --> DB
    API --> FS
    WS --> UDP
    MTX --> FF
    
    UDP --> GA
    UDP --> TA
```

### 技术栈

- **前端应用**: PyQt5 (教师端、小组端)
- **Web前端**: HTML5 + JavaScript + WebSocket (学生端)
- **后端服务**: Flask + SQLAlchemy + Flask-SocketIO
- **数据库**: SQLite (本地存储)
- **视频服务**: MediaMTX + FFmpeg + python-vlc
- **网络通信**: UDP (设备发现) + WebSocket (实时通信)
- **协同白板**: Excalidraw (扩展版本)
- **系统集成**: xdotool (触控回传)

## 组件和接口

### 1. 设备管理模块

#### 设备发现服务
```python
class DeviceDiscoveryService:
    def __init__(self):
        self.udp_port = 8888
        self.devices = {}
    
    def broadcast_presence(self):
        """广播设备存在信息"""
        pass
    
    def discover_devices(self):
        """发现局域网内设备"""
        pass
    
    def register_device(self, device_info):
        """注册设备信息"""
        pass
```

#### 连接管理器
```python
class ConnectionManager:
    def __init__(self):
        self.connections = {}
        self.heartbeat_interval = 30
    
    def establish_connection(self, device_id):
        """建立设备连接"""
        pass
    
    def monitor_connections(self):
        """监控连接状态"""
        pass
    
    def handle_disconnect(self, device_id):
        """处理设备断开"""
        pass
```

### 2. 视频流处理模块

#### 屏幕捕获服务
```python
class ScreenCaptureService:
    def __init__(self):
        self.capture_fps = 30
        self.resolution = "1920x1080"
    
    def start_capture(self, screen_id):
        """开始屏幕捕获"""
        pass
    
    def stop_capture(self, screen_id):
        """停止屏幕捕获"""
        pass
    
    def get_stream_url(self, screen_id):
        """获取流媒体URL"""
        pass
```

#### 视频广播服务
```python
class VideoBroadcastService:
    def __init__(self):
        self.mediamtx_url = "rtmp://localhost:1935"
        self.max_clients = 75
    
    def publish_stream(self, source, stream_key):
        """发布视频流"""
        pass
    
    def subscribe_stream(self, stream_key, client_id):
        """订阅视频流"""
        pass
    
    def get_stream_stats(self, stream_key):
        """获取流统计信息"""
        pass
```

### 3. 协同白板模块

#### 白板引擎
```python
class WhiteboardEngine:
    def __init__(self):
        self.excalidraw_instance = None
        self.collaboration_room = None
    
    def create_room(self, room_id):
        """创建协作房间"""
        pass
    
    def join_room(self, room_id, user_id):
        """加入协作房间"""
        pass
    
    def sync_drawing_data(self, room_id, drawing_data):
        """同步绘图数据"""
        pass
```

#### 批注工具
```python
class AnnotationTool:
    def __init__(self):
        self.tools = ["pen", "highlighter", "eraser", "text"]
        self.colors = ["red", "blue", "green", "yellow", "black"]
    
    def create_annotation(self, tool_type, coordinates, properties):
        """创建批注"""
        pass
    
    def update_annotation(self, annotation_id, properties):
        """更新批注"""
        pass
    
    def delete_annotation(self, annotation_id):
        """删除批注"""
        pass
```

### 4. 分组管理模块

#### 分组服务
```python
class GroupManagementService:
    def __init__(self):
        self.groups = {}
        self.students = {}
    
    def create_group(self, group_id, group_name):
        """创建小组"""
        pass
    
    def assign_student_to_group(self, student_id, group_id):
        """分配学生到小组"""
        pass
    
    def random_grouping(self, student_list, group_count):
        """随机分组"""
        pass
    
    def get_group_status(self, group_id):
        """获取小组状态"""
        pass
```

### 5. 互动答题模块

#### 题目管理
```python
class QuestionManager:
    def __init__(self):
        self.question_types = ["single_choice", "multiple_choice", "true_false", "text"]
    
    def create_question(self, question_data):
        """创建题目"""
        pass
    
    def publish_questions(self, question_list, target_groups):
        """发布题目"""
        pass
    
    def collect_answers(self, question_id):
        """收集答案"""
        pass
```

#### 统计分析
```python
class AnswerAnalytics:
    def __init__(self):
        self.chart_types = ["bar", "pie", "line"]
    
    def generate_statistics(self, question_id):
        """生成统计数据"""
        pass
    
    def create_chart(self, data, chart_type):
        """创建图表"""
        pass
    
    def compare_group_performance(self, question_id, group_ids):
        """对比小组表现"""
        pass
```

### 6. 文件管理模块

#### 文件分发服务
```python
class FileDistributionService:
    def __init__(self):
        self.supported_formats = [
            "pdf", "doc", "docx", "ppt", "pptx", "xls", "xlsx",
            "jpg", "png", "gif", "mp4", "avi", "mp3", "wav"
        ]
    
    def upload_file(self, file_path, metadata):
        """上传文件"""
        pass
    
    def distribute_file(self, file_id, target_groups):
        """分发文件"""
        pass
    
    def track_distribution_status(self, file_id):
        """跟踪分发状态"""
        pass
```

## 数据模型

### 核心数据结构

```python
# 设备信息
class Device:
    device_id: str
    device_type: str  # "teacher", "group", "student"
    ip_address: str
    status: str  # "online", "offline", "busy"
    capabilities: List[str]
    last_heartbeat: datetime

# 课堂信息
class Classroom:
    classroom_id: str
    teacher_id: str
    name: str
    start_time: datetime
    end_time: datetime
    status: str  # "active", "paused", "ended"

# 小组信息
class Group:
    group_id: str
    classroom_id: str
    name: str
    device_id: str
    members: List[str]
    status: str

# 学生信息
class Student:
    student_id: str
    name: str
    group_id: str
    device_info: dict
    attendance_status: str

# 题目信息
class Question:
    question_id: str
    classroom_id: str
    question_type: str
    content: str
    options: List[str]
    correct_answer: str
    created_time: datetime

# 答案信息
class Answer:
    answer_id: str
    question_id: str
    student_id: str
    content: str
    submitted_time: datetime
    is_correct: bool
```

## 错误处理

### 错误分类

1. **网络错误**
   - 连接超时
   - 设备离线
   - 带宽不足

2. **系统错误**
   - 资源不足
   - 权限问题
   - 文件操作失败

3. **业务错误**
   - 无效操作
   - 数据冲突
   - 状态异常

### 错误处理策略

```python
class ErrorHandler:
    def __init__(self):
        self.error_codes = {
            "NETWORK_TIMEOUT": 1001,
            "DEVICE_OFFLINE": 1002,
            "INSUFFICIENT_BANDWIDTH": 1003,
            "RESOURCE_EXHAUSTED": 2001,
            "PERMISSION_DENIED": 2002,
            "FILE_OPERATION_FAILED": 2003,
            "INVALID_OPERATION": 3001,
            "DATA_CONFLICT": 3002,
            "STATE_EXCEPTION": 3003
        }
    
    def handle_error(self, error_type, context):
        """统一错误处理"""
        if error_type in ["NETWORK_TIMEOUT", "DEVICE_OFFLINE"]:
            return self.handle_network_error(error_type, context)
        elif error_type in ["RESOURCE_EXHAUSTED", "PERMISSION_DENIED"]:
            return self.handle_system_error(error_type, context)
        else:
            return self.handle_business_error(error_type, context)
    
    def handle_network_error(self, error_type, context):
        """处理网络错误"""
        # 重试机制
        # 降级服务
        # 用户提示
        pass
    
    def handle_system_error(self, error_type, context):
        """处理系统错误"""
        # 资源清理
        # 日志记录
        # 管理员通知
        pass
    
    def handle_business_error(self, error_type, context):
        """处理业务错误"""
        # 状态回滚
        # 用户提示
        # 操作指导
        pass
```

## 测试策略

### 测试层次

1. **单元测试**
   - 核心算法测试
   - 数据模型验证
   - 工具函数测试

2. **集成测试**
   - 模块间接口测试
   - 数据库操作测试
   - 网络通信测试

3. **系统测试**
   - 端到端功能测试
   - 性能压力测试
   - 兼容性测试

4. **用户验收测试**
   - 教学场景模拟
   - 用户体验测试
   - 稳定性验证

### 性能指标

- **延迟要求**
  - 屏幕共享延迟 < 1.0秒
  - 广播延迟 < 1.5秒
  - 交互响应 < 200ms

- **并发能力**
  - 支持75台终端同时连接
  - 支持8个小组同时研讨
  - 支持6路投屏同时显示

- **稳定性要求**
  - 系统可用性 > 99%
  - 连接成功率 > 95%
  - 数据同步准确率 > 99.9%

### 测试环境

```python
class TestEnvironment:
    def __init__(self):
        self.mock_devices = []
        self.test_classroom = None
        self.performance_monitor = None
    
    def setup_mock_devices(self, device_count):
        """设置模拟设备"""
        pass
    
    def create_test_classroom(self):
        """创建测试课堂"""
        pass
    
    def run_performance_test(self, test_scenario):
        """运行性能测试"""
        pass
    
    def generate_test_report(self):
        """生成测试报告"""
        pass
```