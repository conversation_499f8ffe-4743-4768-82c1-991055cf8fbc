# 智慧课堂系统需求文档

## 介绍

智慧课堂系统是一个面向大学新型教学场景的综合性教学平台，支持多屏研讨式教学。系统运行在统信UOS操作系统上，通过PyQt5开发教师端和小组端应用，使用Flask提供后端服务，学生端通过网页访问。系统旨在实现教师、小组和学生之间的高效互动协作，支持实时屏幕共享、协同批注、互动答题等功能。

## 需求

### 需求1 - 设备连接与管理

**用户故事：** 作为教师，我希望系统能够自动发现和连接小组设备，以便快速建立课堂环境而无需手动配置每个设备。

#### 验收标准

1. WHEN 小组端设备启动 THEN 系统 SHALL 自动检测并建立与教师端的连接
2. WHEN 教师端启动 THEN 系统 SHALL 显示所有可用小组端设备的连接状态
3. WHEN 小组端设备断开连接 THEN 系统 SHALL 在教师端实时更新设备状态
4. WHEN 教师选择开关机操作 THEN 系统 SHALL 能够远程控制小组端屏幕的开关状态

### 需求2 - 小组管理与分组

**用户故事：** 作为教师，我希望能够灵活地管理学生分组，以便根据教学需要快速调整小组构成。

#### 验收标准

1. WHEN 教师访问小组管理界面 THEN 系统 SHALL 显示当前所有学生和分组信息
2. WHEN 教师拖拽学生到不同小组 THEN 系统 SHALL 实时更新分组信息并同步到相关设备
3. WHEN 教师点击随机分组 THEN 系统 SHALL 自动重新分配所有学生到各个小组
4. WHEN 分组发生变化 THEN 系统 SHALL 在所有相关设备上同步显示新的分组状态

### 需求3 - 屏幕共享与广播

**用户故事：** 作为教师，我希望能够实时查看和控制各小组的屏幕内容，以便进行有效的教学指导和内容分享。

#### 验收标准

1. WHEN 教师选择查看小组屏幕 THEN 系统 SHALL 在1.0秒内显示选定小组的实时画面
2. WHEN 教师选择广播内容 THEN 系统 SHALL 在1.5秒内将内容同步到不少于75台终端设备
3. WHEN 教师选择小组屏幕分享 THEN 系统 SHALL 将选定小组的屏幕内容广播到其他小组
4. WHEN 进行屏幕广播 THEN 系统 SHALL 保证画质不低于1080P
5. WHEN 系统支持多屏显示 THEN 系统 SHALL 能够接入不少于8个小组研讨大屏

### 需求4 - 触控交互与远程控制

**用户故事：** 作为教师，我希望能够远程控制小组端设备，以便在不离开讲台的情况下协助学生操作。

#### 验收标准

1. WHEN 小组端投屏到教师端 THEN 系统 SHALL 支持教师端调起小组端虚拟键盘
2. WHEN 教师在教师端进行触控操作 THEN 系统 SHALL 将操作实时传递到小组端设备
3. WHEN 教师进行文字输入 THEN 系统 SHALL 通过触控回传功能在小组端显示输入内容
4. WHEN 触控回传功能启用 THEN 系统 SHALL 支持多点触控和手势操作

### 需求5 - 互动答题与反馈

**用户故事：** 作为教师，我希望能够快速发布题目并收集学生答案，以便实时了解学生的学习情况。

#### 验收标准

1. WHEN 教师发布答题指令 THEN 系统 SHALL 支持单选、多选、判断等多种题型
2. WHEN 教师发布题目 THEN 系统 SHALL 支持一次下发多道题目
3. WHEN 学生提交答案 THEN 系统 SHALL 实时以柱状图形式展示作答结果
4. WHEN 显示答题结果 THEN 系统 SHALL 支持按全班或分组切换展示答题统计
5. WHEN 答题结束 THEN 系统 SHALL 支持对比不同小组的答题情况

### 需求6 - 课堂报告与数据统计

**用户故事：** 作为教师，我希望系统能够自动生成课堂报告，以便课后分析教学效果和学生参与情况。

#### 验收标准

1. WHEN 课堂结束 THEN 系统 SHALL 自动生成包含签到、考勤、互动次数的课堂报告
2. WHEN 生成课堂报告 THEN 系统 SHALL 包含学生参与度、题目详情、答题结果等数据
3. WHEN 查看课堂报告 THEN 系统 SHALL 支持教师添加备注信息
4. WHEN 保存课堂报告 THEN 系统 SHALL 支持导出和历史记录查看功能

### 需求7 - 文件分发与资源管理

**用户故事：** 作为教师，我希望能够快速向学生分发教学资料，以便提高课堂效率。

#### 验收标准

1. WHEN 教师选择文件分发 THEN 系统 SHALL 支持音视频、文档、图片等多种格式
2. WHEN 分发文件 THEN 系统 SHALL 支持向全员或指定小组发送
3. WHEN 文件传输 THEN 系统 SHALL 显示传输进度和状态
4. WHEN 文件接收 THEN 系统 SHALL 在接收端自动保存并通知用户

### 需求8 - 多屏协作与同步显示

**用户故事：** 作为教师，我希望能够控制多个屏幕的显示内容，以便实现灵活的教学展示。

#### 验收标准

1. WHEN 教师控制多屏显示 THEN 系统 SHALL 支持主屏广播、独立显示、小组广播等模式
2. WHEN 设置分组计时 THEN 系统 SHALL 在所有相关设备上同步显示倒计时
3. WHEN 发送讨论主题 THEN 系统 SHALL 将截取的教学内容发送到各小组和学员端
4. WHEN 进行分组讲评 THEN 系统 SHALL 支持单屏、二分屏、四分屏等对比布局
5. WHEN 启用分组协作 THEN 系统 SHALL 支持师生板书内容双向实时同步

### 需求9 - 小组研讨功能

**用户故事：** 作为学生，我希望能够在小组内进行有效的协作讨论，以便完成小组学习任务。

#### 验收标准

1. WHEN 小组进入研讨模式 THEN 系统 SHALL 支持授课研讨和自主研讨两种模式
2. WHEN 学生进行投屏 THEN 系统 SHALL 支持至少6个学员端画面同时显示
3. WHEN 学生进行批注 THEN 系统 SHALL 支持笔芯粗细、颜色选择和撤销恢复功能
4. WHEN 使用电子板书 THEN 系统 SHALL 支持底色选择和内容下载功能
5. WHEN 进行协同书写 THEN 系统 SHALL 支持多人实时编辑和内容同步
6. WHEN 展示作品 THEN 系统 SHALL 支持1-4画面等多种布局方式
7. WHEN 分享资料 THEN 系统 SHALL 支持jpg、png、pdf、doc、docx等格式
8. WHEN 录制讨论 THEN 系统 SHALL 支持整个小组讨论过程的视频录制

### 需求10 - 双屏互动系统

**用户故事：** 作为教师，我希望使用双屏进行教学，以便同时展示不同内容并提高教学效果。

#### 验收标准

1. WHEN 教师开始上课 THEN 系统 SHALL 支持账号密码、扫码和课表关联等登录方式
2. WHEN 学生加入课堂 THEN 系统 SHALL 支持二维码和4/6/9位数字码考勤方式
3. WHEN 使用教学工具 THEN 系统 SHALL 提供画笔批注、电子板书、抢答、投票等功能
4. WHEN 学生投屏展示 THEN 系统 SHALL 支持不少于4个学生同时投屏对比
5. WHEN 学生发送内容 THEN 系统 SHALL 支持文字、图片、文件等多种展示方式
6. WHEN 进行课堂评分 THEN 系统 SHALL 支持个人和小组评分，可设置评分步长
7. WHEN 查看课堂动态 THEN 系统 SHALL 按时间顺序展示评分、答题、弹幕等活动
8. WHEN 查看课堂资料 THEN 系统 SHALL 支持按课程、类型、时间筛选和关键字检索
9. WHEN 布置测验 THEN 系统 SHALL 支持课前预设、本地文件和截屏等方式
10. WHEN 学生答题 THEN 系统 SHALL 支持选择、文字输入和拍照等答题方式
11. WHEN 使用双屏模式 THEN 系统 SHALL 支持不同内容展示和页面联动功能
12. WHEN 展示文件 THEN 系统 SHALL 支持PPT、Word、Excel、PDF、图片、视频等格式
13. WHEN 进行屏幕广播 THEN 系统 SHALL 支持双屏内容的同步显示