# -*- coding: utf-8 -*-
"""
设备发现服务 - 使用UDP广播实现设备发现
"""

import json
import socket
import threading
import time
from datetime import datetime
import logging
from models import Device, DeviceLog

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# UDP广播配置
DISCOVERY_PORT = 5678
BROADCAST_IP = '***************'
DISCOVERY_MESSAGE = json.dumps({
    'type': 'discovery',
    'service': 'smart_classroom',
    'version': '1.0'
}).encode('utf-8')

# 心跳检测配置
HEARTBEAT_INTERVAL = 30  # 秒
OFFLINE_THRESHOLD = 300  # 5分钟无心跳视为离线


class DeviceDiscoveryService:
    """设备发现服务"""
    
    def __init__(self, app=None):
        self.running = False
        self.discovery_thread = None
        self.heartbeat_thread = None
        self.socket = None
        self.discovered_devices = {}  # 存储发现的设备信息
        self.app = app  # Flask应用实例
    
    def start(self):
        """启动设备发现服务"""
        if self.running:
            logger.warning("设备发现服务已经在运行")
            return False
        
        try:
            # 创建UDP套接字
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind(('', DISCOVERY_PORT))
            
            self.running = True
            
            # 启动发现线程
            self.discovery_thread = threading.Thread(target=self._discovery_loop)
            self.discovery_thread.daemon = True
            self.discovery_thread.start()
            
            # 启动心跳检测线程
            self.heartbeat_thread = threading.Thread(target=self._heartbeat_check_loop)
            self.heartbeat_thread.daemon = True
            self.heartbeat_thread.start()
            
            logger.info("设备发现服务已启动")
            return True
        
        except Exception as e:
            logger.error(f"启动设备发现服务失败: {str(e)}")
            self.stop()
            return False
    
    def stop(self):
        """停止设备发现服务"""
        self.running = False
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        
        if self.discovery_thread:
            self.discovery_thread.join(timeout=1.0)
            self.discovery_thread = None
        
        if self.heartbeat_thread:
            self.heartbeat_thread.join(timeout=1.0)
            self.heartbeat_thread = None
        
        logger.info("设备发现服务已停止")
    
    def broadcast_discovery(self):
        """发送设备发现广播"""
        if not self.running or not self.socket:
            logger.warning("设备发现服务未运行，无法发送广播")
            return False
        
        try:
            self.socket.sendto(DISCOVERY_MESSAGE, (BROADCAST_IP, DISCOVERY_PORT))
            logger.info("已发送设备发现广播")
            return True
        except Exception as e:
            logger.error(f"发送设备发现广播失败: {str(e)}")
            return False
    
    def _discovery_loop(self):
        """设备发现循环"""
        logger.info("设备发现线程已启动")
        
        while self.running:
            try:
                # 发送广播
                self.broadcast_discovery()
                
                # 接收响应
                self._listen_for_responses()
                
                # 每30秒广播一次
                time.sleep(30)
            
            except Exception as e:
                logger.error(f"设备发现循环出错: {str(e)}")
                time.sleep(5)  # 出错后等待5秒再继续
    
    def _listen_for_responses(self):
        """监听设备响应"""
        if not self.running or not self.socket:
            return
        
        # 设置超时，避免无限等待
        self.socket.settimeout(5.0)
        
        start_time = time.time()
        # 监听10秒
        while self.running and (time.time() - start_time) < 10:
            try:
                data, addr = self.socket.recvfrom(1024)
                self._process_device_response(data, addr)
            
            except socket.timeout:
                pass
            except Exception as e:
                logger.error(f"接收设备响应出错: {str(e)}")
        
        # 恢复为阻塞模式
        self.socket.settimeout(None)
    
    def _process_device_response(self, data, addr):
        """处理设备响应"""
        try:
            response = json.loads(data.decode('utf-8'))
            
            if response.get('type') == 'discovery_response' and response.get('service') == 'smart_classroom':
                device_id = response.get('device_id')
                device_type = response.get('device_type')
                device_name = response.get('device_name')
                capabilities = response.get('capabilities', [])
                
                if not device_id or not device_type or not device_name:
                    logger.warning(f"收到无效的设备响应: {response}")
                    return
                
                ip_address = addr[0]
                port = response.get('port', addr[1])
                
                # 更新发现的设备列表
                self.discovered_devices[device_id] = {
                    'device_id': device_id,
                    'device_type': device_type,
                    'device_name': device_name,
                    'ip_address': ip_address,
                    'port': port,
                    'capabilities': capabilities,
                    'last_seen': datetime.utcnow(),
                    'os_info': response.get('os_info'),
                    'screen_resolution': response.get('screen_resolution')
                }
                
                # 注册或更新设备
                if self.app:
                    try:
                        # 为设备注册创建一个新的应用上下文
                        with self.app.app_context():
                            from .device_service import DeviceService
                            DeviceService.register_device(
                                device_id=device_id,
                                device_type=device_type,
                                device_name=device_name,
                                ip_address=ip_address,
                                port=port,
                                capabilities=capabilities,
                                screen_resolution=response.get('screen_resolution'),
                                os_info=response.get('os_info')
                            )
                    except Exception as e:
                        logger.error(f"注册设备时出错: {str(e)}")
                else:
                    logger.warning("Flask应用实例未设置，无法注册设备到数据库")
                
                logger.info(f"发现设备: {device_name} ({device_id}) at {ip_address}:{port}")
        
        except json.JSONDecodeError:
            logger.warning(f"收到无效的JSON数据: {data}")
        except Exception as e:
            logger.error(f"处理设备响应出错: {str(e)}")
    
    def _heartbeat_check_loop(self):
        """心跳检测循环"""
        logger.info("心跳检测线程已启动")
        
        while self.running:
            try:
                # 清理离线设备
                if self.app:
                    try:
                        # 为每次循环创建一个新的应用上下文
                        with self.app.app_context():
                            from .device_service import DeviceService
                            offline_count = DeviceService.cleanup_offline_devices()
                            if offline_count > 0:
                                logger.info(f"已将 {offline_count} 台设备标记为离线")
                    except Exception as e:
                        logger.error(f"心跳检测清理设备时出错: {str(e)}")
                else:
                    logger.warning("Flask应用实例未设置，无法执行心跳检测")
                
                # 每30秒检查一次
                time.sleep(HEARTBEAT_INTERVAL)
            
            except Exception as e:
                logger.error(f"心跳检测循环出错: {str(e)}")
                time.sleep(5)  # 出错后等待5秒再继续
    
    def get_discovered_devices(self):
        """获取发现的设备列表"""
        return list(self.discovered_devices.values())


# 单例模式
_discovery_service = None

def get_discovery_service(app=None):
    """获取设备发现服务实例"""
    global _discovery_service
    if _discovery_service is None:
        _discovery_service = DeviceDiscoveryService(app)
    elif app and not _discovery_service.app:
        _discovery_service.app = app
    return _discovery_service

def init_discovery_service(app):
    """初始化设备发现服务"""
    service = get_discovery_service(app)
    return service