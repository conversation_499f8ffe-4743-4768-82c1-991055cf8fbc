# -*- coding: utf-8 -*-
"""
课堂管理业务服务
"""

from datetime import datetime, timedelta
from models import Classroom, Student, Group, Device, ClassroomActivity
from models.base import db


class ClassroomService:
    """课堂管理服务"""
    
    @staticmethod
    def create_classroom(teacher_id, teacher_name, name, description=None, duration=None):
        """创建课堂"""
        classroom_id = f"class_{teacher_id}_{int(datetime.utcnow().timestamp())}"
        
        classroom = Classroom(
            classroom_id=classroom_id,
            name=name,
            description=description,
            teacher_id=teacher_id,
            teacher_name=teacher_name,
            start_time=datetime.utcnow(),
            duration=duration,
            status='active'  # 直接设置为活跃状态
        )
        
        # 生成访问码
        classroom.generate_access_code()
        classroom.save()
        
        # 记录活动
        ClassroomActivity.log_activity(
            classroom.id, 'create', teacher_id, teacher_name,
            f"创建课堂: {name}"
        )
        
        return classroom
    
    @staticmethod
    def start_classroom(classroom_id, teacher_id):
        """开始课堂"""
        classroom = Classroom.get_by_classroom_id(classroom_id)
        if not classroom:
            return None, "课堂不存在"
        
        if classroom.teacher_id != teacher_id:
            return None, "无权限操作"
        
        classroom.start_class()
        
        # 记录活动
        ClassroomActivity.log_activity(
            classroom.id, 'start', teacher_id, classroom.teacher_name,
            "开始上课"
        )
        
        return classroom, "课堂已开始"
    
    @staticmethod
    def end_classroom(classroom_id, teacher_id):
        """结束课堂"""
        classroom = Classroom.get_by_classroom_id(classroom_id)
        if not classroom:
            return None, "课堂不存在"
        
        if classroom.teacher_id != teacher_id:
            return None, "无权限操作"
        
        classroom.end_class()
        
        # 记录活动
        ClassroomActivity.log_activity(
            classroom.id, 'end', teacher_id, classroom.teacher_name,
            "结束课堂"
        )
        
        return classroom, "课堂已结束"
    
    @staticmethod
    def join_classroom(access_code, student_id, student_name, device_info=None):
        """学生加入课堂"""
        classroom = Classroom.get_by_access_code(access_code)
        if not classroom:
            return None, "课堂不存在或访问码错误"
        
        if not classroom.is_active():
            return None, "课堂未开始或已结束"
        
        # 检查学生是否已存在
        student = Student.get_by_student_id(student_id)
        if student and student.classroom_id == classroom.id:
            student.check_in('access_code')
            return student, "重新加入成功"
        
        # 创建新学生记录
        student = Student(
            student_id=student_id,
            name=student_name,
            classroom_id=classroom.id,
            device_info=device_info or {}
        )
        student.check_in('access_code')
        student.save()
        
        # 更新课堂统计
        classroom.update_statistics()
        
        # 记录活动
        ClassroomActivity.log_activity(
            classroom.id, 'join', student_id, student_name,
            "加入课堂"
        )
        
        return student, "加入成功"
    
    @staticmethod
    def get_classroom_status(classroom_id):
        """获取课堂状态"""
        classroom = Classroom.get_by_classroom_id(classroom_id)
        if not classroom:
            return None
        
        # 获取统计信息
        online_students = Student.get_online_students(classroom.id)
        present_students = Student.get_present_students(classroom.id)
        groups = Group.get_classroom_groups(classroom.id)
        devices = Device.get_devices_by_classroom(classroom.id)
        
        return {
            'classroom': classroom.to_dict(),
            'statistics': {
                'total_students': classroom.total_students,
                'online_students': len(online_students),
                'present_students': len(present_students),
                'total_groups': len(groups),
                'connected_devices': len([d for d in devices if d.is_online()])
            },
            'duration_minutes': classroom.get_duration_minutes()
        }
    
    @staticmethod
    def create_groups(classroom_id, group_count, group_prefix="小组"):
        """为课堂创建小组"""
        classroom = Classroom.get_by_classroom_id(classroom_id)
        if not classroom:
            return None, "课堂不存在"
        
        groups = Group.create_groups_for_classroom(
            classroom.id, group_count, group_prefix
        )
        
        classroom.update_statistics()
        
        return groups, f"成功创建{len(groups)}个小组"
    
    @staticmethod
    def assign_student_to_group(student_id, group_id):
        """分配学生到小组"""
        student = Student.get_by_student_id(student_id)
        group = Group.get_by_id(group_id)
        
        if not student or not group:
            return False, "学生或小组不存在"
        
        if student.classroom_id != group.classroom_id:
            return False, "学生和小组不在同一课堂"
        
        # 如果学生已在其他小组，先移除
        if student.group_id:
            old_group = Group.get_by_id(student.group_id)
            if old_group:
                old_group.remove_student(student)
        
        # 添加到新小组
        success, message = group.add_student(student)
        
        if success:
            # 记录活动
            ClassroomActivity.log_activity(
                student.classroom_id, 'group_assign', 
                student.student_id, student.name,
                f"分配到{group.name}"
            )
        
        return success, message
    
    @staticmethod
    def auto_assign_groups(classroom_id, strategy='random'):
        """自动分组"""
        classroom = Classroom.get_by_classroom_id(classroom_id)
        if not classroom:
            return False, "课堂不存在"
        
        students = Student.get_classroom_students(classroom.id)
        groups = Group.get_classroom_groups(classroom.id)
        
        if not students or not groups:
            return False, "没有学生或小组"
        
        # 清空现有分组
        for student in students:
            if student.group_id:
                group = Group.get_by_id(student.group_id)
                if group:
                    group.remove_student(student)
        
        if strategy == 'random':
            import random
            random.shuffle(students)
        
        # 平均分配
        group_index = 0
        for student in students:
            group = groups[group_index % len(groups)]
            if not group.is_full():
                group.add_student(student)
            group_index += 1
        
        return True, f"成功分配{len(students)}名学生到{len(groups)}个小组"