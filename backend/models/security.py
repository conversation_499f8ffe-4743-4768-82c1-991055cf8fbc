# -*- coding: utf-8 -*-
"""
安全和权限管理数据模型
"""

import hashlib
import secrets
import datetime
from enum import Enum
from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON>an, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from .base import BaseModel, db


class UserRole(Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    TEACHER = "teacher"
    STUDENT = "student"
    GUEST = "guest"


class Permission(Enum):
    """权限枚举"""
    # 系统管理权限
    SYSTEM_ADMIN = "system_admin"
    USER_MANAGEMENT = "user_management"
    DEVICE_MANAGEMENT = "device_management"
    
    # 教学权限
    CLASSROOM_CREATE = "classroom_create"
    CLASSROOM_MANAGE = "classroom_manage"
    CLASSROOM_DELETE = "classroom_delete"
    
    # 小组管理权限
    GROUP_CREATE = "group_create"
    GROUP_MANAGE = "group_manage"
    GROUP_DELETE = "group_delete"
    
    # 内容权限
    CONTENT_CREATE = "content_create"
    CONTENT_EDIT = "content_edit"
    CONTENT_DELETE = "content_delete"
    CONTENT_SHARE = "content_share"
    
    # 文件权限
    FILE_UPLOAD = "file_upload"
    FILE_DOWNLOAD = "file_download"
    FILE_DELETE = "file_delete"
    FILE_SHARE = "file_share"
    
    # 视频权限
    VIDEO_BROADCAST = "video_broadcast"
    VIDEO_RECORD = "video_record"
    VIDEO_VIEW = "video_view"
    
    # 白板权限
    WHITEBOARD_CREATE = "whiteboard_create"
    WHITEBOARD_EDIT = "whiteboard_edit"
    WHITEBOARD_VIEW = "whiteboard_view"
    
    # 答题权限
    QUESTION_CREATE = "question_create"
    QUESTION_PUBLISH = "question_publish"
    QUESTION_VIEW_RESULTS = "question_view_results"
    
    # 报告权限
    REPORT_VIEW = "report_view"
    REPORT_EXPORT = "report_export"


class User(BaseModel):
    """用户模型"""
    __tablename__ = 'users'
    
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=True, index=True)
    password_hash = Column(String(255), nullable=False)
    salt = Column(String(32), nullable=False)
    role = Column(String(20), nullable=False, default=UserRole.STUDENT.value)
    name = Column(String(100), nullable=False)
    phone = Column(String(20), nullable=True)
    department = Column(String(100), nullable=True)
    
    # 账户状态
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    last_login = Column(DateTime, nullable=True)
    failed_login_attempts = Column(Integer, default=0)
    locked_until = Column(DateTime, nullable=True)
    
    # 密码策略
    password_expires_at = Column(DateTime, nullable=True)
    must_change_password = Column(Boolean, default=False)
    
    # 关联关系
    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    permissions = relationship("UserPermission", back_populates="user", foreign_keys="UserPermission.user_id", cascade="all, delete-orphan")
    audit_logs = relationship("AuditLog", back_populates="user")
    
    def set_password(self, password):
        """设置密码"""
        self.salt = secrets.token_hex(16)
        self.password_hash = self._hash_password(password, self.salt)
        self.password_expires_at = datetime.datetime.utcnow() + datetime.timedelta(days=90)
    
    def check_password(self, password):
        """验证密码"""
        return self.password_hash == self._hash_password(password, self.salt)
    
    def _hash_password(self, password, salt):
        """密码哈希"""
        return hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000).hex()
    
    def is_locked(self):
        """检查账户是否被锁定"""
        if self.locked_until and self.locked_until > datetime.datetime.utcnow():
            return True
        return False
    
    def lock_account(self, duration_minutes=30):
        """锁定账户"""
        self.locked_until = datetime.datetime.utcnow() + datetime.timedelta(minutes=duration_minutes)
        self.failed_login_attempts = 0
    
    def unlock_account(self):
        """解锁账户"""
        self.locked_until = None
        self.failed_login_attempts = 0
    
    def has_permission(self, permission):
        """检查用户是否有特定权限"""
        # 管理员拥有所有权限
        if self.role == UserRole.ADMIN.value:
            return True
        
        # 检查用户特定权限
        for user_perm in self.permissions:
            if user_perm.permission == permission.value:
                return True
        
        # 检查角色默认权限
        return self._has_role_permission(permission)
    
    def _has_role_permission(self, permission):
        """检查角色默认权限"""
        role_permissions = {
            UserRole.TEACHER.value: [
                Permission.CLASSROOM_CREATE, Permission.CLASSROOM_MANAGE,
                Permission.GROUP_CREATE, Permission.GROUP_MANAGE,
                Permission.CONTENT_CREATE, Permission.CONTENT_EDIT, Permission.CONTENT_SHARE,
                Permission.FILE_UPLOAD, Permission.FILE_DOWNLOAD, Permission.FILE_SHARE,
                Permission.VIDEO_BROADCAST, Permission.VIDEO_RECORD, Permission.VIDEO_VIEW,
                Permission.WHITEBOARD_CREATE, Permission.WHITEBOARD_EDIT, Permission.WHITEBOARD_VIEW,
                Permission.QUESTION_CREATE, Permission.QUESTION_PUBLISH, Permission.QUESTION_VIEW_RESULTS,
                Permission.REPORT_VIEW, Permission.REPORT_EXPORT
            ],
            UserRole.STUDENT.value: [
                Permission.CONTENT_CREATE, Permission.FILE_DOWNLOAD,
                Permission.VIDEO_VIEW, Permission.WHITEBOARD_VIEW,
                Permission.WHITEBOARD_EDIT
            ],
            UserRole.GUEST.value: [
                Permission.VIDEO_VIEW, Permission.WHITEBOARD_VIEW
            ]
        }
        
        role_perms = role_permissions.get(self.role, [])
        return permission in role_perms


class UserSession(BaseModel):
    """用户会话模型"""
    __tablename__ = 'user_sessions'
    
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    session_token = Column(String(255), unique=True, nullable=False, index=True)
    refresh_token = Column(String(255), unique=True, nullable=True)
    device_info = Column(JSON, nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    
    # 会话状态
    is_active = Column(Boolean, default=True)
    expires_at = Column(DateTime, nullable=False)
    last_activity = Column(DateTime, default=datetime.datetime.utcnow)
    
    # 关联关系
    user = relationship("User", back_populates="sessions")
    
    def is_expired(self):
        """检查会话是否过期"""
        return datetime.datetime.utcnow() > self.expires_at
    
    def extend_session(self, hours=24):
        """延长会话"""
        self.expires_at = datetime.datetime.utcnow() + datetime.timedelta(hours=hours)
        self.last_activity = datetime.datetime.utcnow()


class UserPermission(BaseModel):
    """用户权限模型"""
    __tablename__ = 'user_permissions'
    
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    permission = Column(String(50), nullable=False)
    granted_by = Column(Integer, ForeignKey('users.id'), nullable=True)
    granted_at = Column(DateTime, default=datetime.datetime.utcnow)
    expires_at = Column(DateTime, nullable=True)
    
    # 关联关系
    user = relationship("User", back_populates="permissions", foreign_keys=[user_id])
    granter = relationship("User", foreign_keys=[granted_by])


class DeviceAccess(BaseModel):
    """设备访问控制模型"""
    __tablename__ = 'device_access'
    
    device_id = Column(String(100), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    access_type = Column(String(20), nullable=False)  # 'allowed', 'denied', 'restricted'
    
    # 访问限制
    allowed_operations = Column(JSON, nullable=True)  # 允许的操作列表
    time_restrictions = Column(JSON, nullable=True)   # 时间限制
    ip_restrictions = Column(JSON, nullable=True)     # IP限制
    
    # 关联关系
    user = relationship("User")


class AuditLog(BaseModel):
    """审计日志模型"""
    __tablename__ = 'audit_logs'
    
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    action = Column(String(100), nullable=False)
    resource_type = Column(String(50), nullable=True)
    resource_id = Column(String(100), nullable=True)
    
    # 请求信息
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    request_method = Column(String(10), nullable=True)
    request_url = Column(String(500), nullable=True)
    
    # 操作详情
    details = Column(JSON, nullable=True)
    result = Column(String(20), nullable=False)  # 'success', 'failure', 'error'
    error_message = Column(Text, nullable=True)
    
    # 关联关系
    user = relationship("User", back_populates="audit_logs")


class SecurityConfig(BaseModel):
    """安全配置模型"""
    __tablename__ = 'security_configs'
    
    key = Column(String(100), unique=True, nullable=False)
    value = Column(JSON, nullable=False)
    description = Column(Text, nullable=True)
    
    # 配置元信息
    category = Column(String(50), nullable=False)
    is_sensitive = Column(Boolean, default=False)
    last_modified_by = Column(Integer, ForeignKey('users.id'), nullable=True)
    
    # 关联关系
    modifier = relationship("User")


class DataEncryption(BaseModel):
    """数据加密记录模型"""
    __tablename__ = 'data_encryption'
    
    data_type = Column(String(50), nullable=False)
    data_id = Column(String(100), nullable=False)
    encryption_algorithm = Column(String(50), nullable=False)
    key_id = Column(String(100), nullable=False)
    
    # 加密状态
    is_encrypted = Column(Boolean, default=True)
    encrypted_at = Column(DateTime, default=datetime.datetime.utcnow)
    
    # 索引
    __table_args__ = (
        db.Index('idx_data_type_id', 'data_type', 'data_id'),
    )