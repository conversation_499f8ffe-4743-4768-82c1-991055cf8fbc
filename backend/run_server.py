#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统 - 启动Flask服务器
"""

# 在导入Flask和SocketIO之前进行monkey patching
import eventlet
eventlet.monkey_patch()

import os
import sys

# 确保可以导入app模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# 确保instance目录存在
instance_dir = os.path.join(current_dir, 'instance')
if not os.path.exists(instance_dir):
    os.makedirs(instance_dir)
    print(f"创建数据库目录: {instance_dir}")

# 导入应用
from backend.app import app, socketio

if __name__ == '__main__':
    print("启动智慧课堂系统后端服务...")
    print("服务将运行在 http://localhost:5000")
    print("按 Ctrl+C 停止服务")
    
    # 创建数据库表
    with app.app_context():
        from backend.models import db
        db.create_all()
        print("数据库表已创建")
    
    # 启动服务
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)