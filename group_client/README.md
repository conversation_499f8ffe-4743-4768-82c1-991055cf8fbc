# 智慧课堂系统 - 小组端

## 连接问题排查指南

如果遇到"小组端连接服务器失败 HTTP 400"错误，请按照以下步骤进行排查：

### 1. 检查服务器配置

确保小组端配置中的服务器地址和端口正确。默认配置使用`localhost:5000`，这仅适用于小组端和服务器在同一台机器上运行的情况。

如果小组端和服务器在不同机器上，请创建或修改`local_config.py`文件：

```python
# 在group_client/local_config.py中
from shared.config import Config

class LocalConfig(Config):
    # 修改为服务器的实际IP地址
    BACKEND_HOST = "*************"  # 替换为服务器的实际IP地址
    BACKEND_PORT = 5000
```

### 2. 运行连接测试工具

使用提供的连接测试工具检查与服务器的连接：

```bash
python -m group_client.tools.connection_test
```

### 3. 检查网络连接

- 确保小组端和服务器之间的网络连接正常
- 检查防火墙设置，确保允许端口5000的TCP连接
- 使用ping命令测试基本连接：`ping 服务器IP地址`

### 4. 检查服务器状态

- 确保后端服务器正在运行
- 检查服务器日志中是否有错误信息
- 尝试在浏览器中访问`http://服务器IP:5000/api/health`测试服务器健康状态

### 5. 检查WebSocket连接

小组端使用WebSocket连接接收实时更新。如果WebSocket连接失败，可能会导致某些功能不可用。

### 6. 常见错误代码

- **HTTP 400 (Bad Request)**: 请求参数不正确或缺少必要参数
  - 对于`/api/groups`接口，必须提供`classroom_id`参数
  - 例如：`/api/groups?classroom_id=123`
- **HTTP 401 (Unauthorized)**: 身份验证失败
- **HTTP 403 (Forbidden)**: 没有权限访问资源
- **HTTP 404 (Not Found)**: 请求的资源不存在
- **HTTP 500 (Internal Server Error)**: 服务器内部错误

### 7. 特定问题排查

#### "小组端连接服务器失败 HTTP 400"

这个错误通常是因为在请求`/api/groups`接口时没有提供必要的`classroom_id`参数。解决方法：

1. 确保使用最新版本的小组端客户端，已修复此问题
2. 如果问题仍然存在，请检查网络连接并运行连接测试工具
3. 确保服务器上有可用的课堂，或者使用手动输入课堂ID的方式连接

#### "WebSocket错误 (1): The remote host closed the connection"

这个错误通常是因为WebSocket连接被服务器关闭。解决方法：

1. 确保使用最新版本的小组端客户端，已修复此问题（使用Socket.IO客户端库）
2. 检查服务器是否正常运行，并且Socket.IO服务已启动
3. 确保网络连接稳定，没有防火墙阻止WebSocket连接
4. 如果问题仍然存在，请尝试重启小组端应用和服务器

如果问题仍然存在，请联系系统管理员获取帮助。