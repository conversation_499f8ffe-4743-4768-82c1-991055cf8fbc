#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小组端视频面板
"""

import os
import sys
import json
import requests
import logging
import threading
import time
import socketio
from typing import Dict, List

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QGridLayout,
    QScrollArea, QFrame, QSplitter, QComboBox, QMessageBox, QMenu, QAction,
    QDialog, QDialogButtonBox, QListWidget, QListWidgetItem, QCheckBox
)
from PyQt5.QtCore import Qt, QSize, QUrl, QTimer, pyqtSignal, pyqtSlot, QThread
from PyQt5.QtGui import QIcon, QPixmap
from PyQt5.QtMultimedia import QMediaContent, QMediaPlayer
from PyQt5.QtMultimediaWidgets import QVideoWidget

from group_client.config_loader import get_config

# 获取配置
config = get_config()

# 配置日志
logger = logging.getLogger('video_panel')

class SocketIOThread(QThread):
    """Socket.IO客户端线程"""
    
    # 定义信号
    connected = pyqtSignal()
    disconnected = pyqtSignal()
    error = pyqtSignal(str)
    message_received = pyqtSignal(str, object)
    
    def __init__(self, host, port, parent=None):
        super().__init__(parent)
        self.host = host
        self.port = port
        self.sio = socketio.Client()
        self.running = False
        
        # 注册Socket.IO事件处理函数
        self.sio.on('connect', self._on_connect)
        self.sio.on('disconnect', self._on_disconnect)
        self.sio.on('stream_started', lambda data: self._on_event('stream_started', data))
        self.sio.on('stream_stopped', lambda data: self._on_event('stream_stopped', data))
        self.sio.on('stream_broadcast', lambda data: self._on_event('stream_broadcast', data))
    
    def run(self):
        """线程运行函数"""
        self.running = True
        try:
            # 连接到Socket.IO服务器
            server_url = f"http://{self.host}:{self.port}"
            logger.info(f"正在连接Socket.IO服务器: {server_url}")
            self.sio.connect(server_url, transports=['websocket'])
            
            # 保持线程运行，直到停止
            while self.running:
                time.sleep(0.1)
        
        except Exception as e:
            logger.error(f"Socket.IO连接错误: {str(e)}")
            self.error.emit(str(e))
        
        finally:
            # 确保断开连接
            if self.sio.connected:
                self.sio.disconnect()
    
    def stop(self):
        """停止线程"""
        self.running = False
        if self.sio.connected:
            self.sio.disconnect()
        self.wait()
    
    def emit(self, event, data=None):
        """发送事件"""
        if self.sio.connected:
            try:
                self.sio.emit(event, data)
                return True
            except Exception as e:
                logger.error(f"发送事件 {event} 失败: {str(e)}")
                return False
        else:
            logger.warning(f"Socket.IO未连接，无法发送事件: {event}")
            return False
    
    def join_room(self, room):
        """加入房间"""
        return self.emit('join_group', {'group_id': room})
    
    def leave_room(self, room):
        """离开房间"""
        return self.emit('leave_group', {'group_id': room})
    
    def _on_connect(self):
        """连接成功处理"""
        logger.info("Socket.IO已连接")
        self.connected.emit()
    
    def _on_disconnect(self):
        """断开连接处理"""
        logger.warning("Socket.IO已断开")
        self.disconnected.emit()
    
    def _on_event(self, event, data):
        """事件处理"""
        logger.info(f"收到事件: {event}")
        self.message_received.emit(event, data)

class VideoStream(QWidget):
    """视频流显示组件"""
    
    def __init__(self, stream_id, source_id, stream_type, url, parent=None):
        super().__init__(parent)
        self.stream_id = stream_id
        self.source_id = source_id
        self.stream_type = stream_type
        self.url = url
        self.parent = parent
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建视频播放器
        self.video_widget = QVideoWidget()
        self.video_widget.setMinimumSize(320, 240)
        
        self.media_player = QMediaPlayer(self)
        self.media_player.setVideoOutput(self.video_widget)
        
        # 设置视频源
        self.media_player.setMedia(QMediaContent(QUrl(self.url)))
        
        # 添加视频控件
        layout.addWidget(self.video_widget)
        
        # 创建控制栏
        control_layout = QHBoxLayout()
        
        # 添加标题标签
        title = f"{self.source_id} ({self.stream_type})"
        title_label = QLabel(title)
        control_layout.addWidget(title_label)
        
        # 添加控制按钮
        control_layout.addStretch()
        
        # 停止按钮
        stop_btn = QPushButton("停止")
        stop_btn.clicked.connect(self.stop_stream)
        control_layout.addWidget(stop_btn)
        
        # 全屏按钮
        fullscreen_btn = QPushButton("全屏")
        fullscreen_btn.clicked.connect(self.toggle_fullscreen)
        control_layout.addWidget(fullscreen_btn)
        
        # 添加控制栏到布局
        layout.addLayout(control_layout)
        
        # 开始播放
        self.media_player.play()
    
    def stop_stream(self):
        """停止流"""
        if self.parent:
            self.parent.stop_stream(self.stream_id)
    
    def toggle_fullscreen(self):
        """切换全屏显示"""
        if self.video_widget.isFullScreen():
            self.video_widget.setFullScreen(False)
        else:
            self.video_widget.setFullScreen(True)
    
    def closeEvent(self, event):
        """关闭事件处理"""
        # 停止播放器
        self.media_player.stop()
        event.accept()

class VideoPanel(QWidget):
    """视频面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.streams = {}  # 存储流信息
        self.stream_widgets = {}  # 存储流组件
        self.current_layout = "grid"  # 当前布局模式：grid或single
        self.current_stream = None  # 当前选中的流
        self.group_id = None  # 当前小组ID
        self.mode = "teaching"  # 当前模式：teaching或independent
        self.socketio_thread = None  # Socket.IO线程
        
        # 初始化UI
        self.init_ui()
        
        # 初始化Socket.IO连接
        self.init_socketio()
    
    def init_ui(self):
        """初始化UI"""
        # 创建主布局
        layout = QVBoxLayout(self)
        
        # 创建控制栏
        control_layout = QHBoxLayout()
        
        # 添加布局选择下拉框
        layout_label = QLabel("布局:")
        control_layout.addWidget(layout_label)
        
        self.layout_combo = QComboBox()
        self.layout_combo.addItem("网格布局", "grid")
        self.layout_combo.addItem("单视图", "single")
        self.layout_combo.currentIndexChanged.connect(self.change_layout)
        control_layout.addWidget(self.layout_combo)
        
        # 添加刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh)
        control_layout.addWidget(refresh_btn)
        
        # 添加控制栏到主布局
        layout.addLayout(control_layout)
        
        # 创建滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setFrameShape(QFrame.NoFrame)
        
        # 创建内容区域
        self.content_widget = QWidget()
        self.grid_layout = QGridLayout(self.content_widget)
        self.grid_layout.setContentsMargins(0, 0, 0, 0)
        self.grid_layout.setSpacing(10)
        
        self.scroll_area.setWidget(self.content_widget)
        layout.addWidget(self.scroll_area)
        
        # 创建状态标签
        self.status_label = QLabel("视频流: 0")
        layout.addWidget(self.status_label)
    
    def init_socketio(self):
        """初始化Socket.IO连接"""
        # 创建并启动Socket.IO线程
        self.socketio_thread = SocketIOThread(config.BACKEND_HOST, config.BACKEND_PORT, self)
        
        # 连接信号
        self.socketio_thread.connected.connect(self.on_socketio_connected)
        self.socketio_thread.disconnected.connect(self.on_socketio_disconnected)
        self.socketio_thread.error.connect(self.on_socketio_error)
        self.socketio_thread.message_received.connect(self.on_socketio_message)
        
        # 启动线程
        self.socketio_thread.start()
    
    def on_socketio_error(self, error_message):
        """处理Socket.IO错误"""
        logger.error(f"Socket.IO错误: {error_message}")
        
        if hasattr(self, 'parent') and self.parent:
            self.parent.show_error(f"Socket.IO连接错误: {error_message}")
    
    def on_socketio_connected(self):
        """Socket.IO连接成功"""
        logger.info("Socket.IO已连接")
        
        # 如果已加入小组，发送加入小组房间消息
        if self.group_id:
            self.join_group_room()
    
    def on_socketio_disconnected(self):
        """Socket.IO断开连接"""
        logger.warning(f"Socket.IO已断开，服务器地址: {config.BACKEND_HOST}:{config.BACKEND_PORT}")
        
        # 显示连接错误信息
        if hasattr(self, 'parent') and self.parent:
            self.parent.show_error(f"与服务器的Socket.IO连接已断开，将在5秒后尝试重新连接")
        
        # 尝试重新连接
        QTimer.singleShot(5000, self.reconnect_socketio)
    
    def reconnect_socketio(self):
        """重新连接Socket.IO"""
        # 停止旧线程
        if self.socketio_thread and self.socketio_thread.isRunning():
            self.socketio_thread.stop()
        
        # 初始化新连接
        self.init_socketio()
    
    def on_socketio_message(self, event_name, data):
        """处理Socket.IO消息"""
        try:
            # 处理不同类型的事件
            if event_name == 'stream_started':
                self.handle_stream_started(data)
            elif event_name == 'stream_stopped':
                self.handle_stream_stopped(data)
            elif event_name == 'stream_broadcast':
                self.handle_stream_broadcast(data)
        except Exception as e:
            logger.error(f"处理Socket.IO消息时出错: {str(e)}")
    
    def handle_stream_started(self, data):
        """处理新流可用事件"""
        stream_id = data.get('stream_id')
        source_id = data.get('source_id')
        stream_type = data.get('stream_type')
        http_url = data.get('http_url')
        
        # 添加流
        self.add_stream({
            'stream_id': stream_id,
            'source_id': source_id,
            'stream_type': stream_type,
            'http_url': http_url
        })
    
    def handle_stream_stopped(self, data):
        """处理流停止事件"""
        stream_id = data.get('stream_id')
        
        # 移除流
        self.remove_stream(stream_id)
    
    def handle_stream_broadcast(self, data):
        """处理广播流事件"""
        stream_id = data.get('stream_id')
        source_id = data.get('source_id')
        stream_type = data.get('stream_type')
        http_url = data.get('http_url')
        target_group = data.get('target_group')
        
        # 检查是否是发给当前小组的广播
        if target_group == self.group_id:
            # 添加流
            self.add_stream({
                'stream_id': stream_id,
                'source_id': source_id,
                'stream_type': stream_type,
                'http_url': http_url
            })
    
    def join_group_room(self):
        """加入小组Socket.IO房间"""
        if self.socketio_thread and self.group_id:
            # 发送加入小组房间消息
            success = self.socketio_thread.join_room(self.group_id)
            if success:
                logger.info(f"已加入小组房间: {self.group_id}")
            else:
                logger.warning(f"加入小组房间失败: {self.group_id}")
    
    def on_group_joined(self, group_id):
        """处理加入小组事件"""
        self.group_id = group_id
        
        # 加入WebSocket小组房间
        self.join_group_room()
        
        # 刷新流列表
        self.refresh()
    
    def on_mode_changed(self, mode):
        """处理模式切换事件"""
        self.mode = mode
        logger.info(f"视频面板切换到模式: {mode}")
    
    def refresh(self):
        """刷新视频流列表"""
        if not self.group_id:
            return
        
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 发送请求获取流列表
            response = requests.get(f"{api_base_url}/video/streams")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.update_streams(data.get('streams', []))
                else:
                    self.parent.show_error(f"获取视频流列表失败: {data.get('message')}")
            else:
                self.parent.show_error(f"获取视频流列表失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.parent.show_error(f"刷新视频流列表时出错: {str(e)}")
    
    def update_streams(self, streams):
        """更新视频流列表"""
        # 记录当前流ID
        current_stream_ids = set(self.streams.keys())
        new_stream_ids = set()
        
        # 添加或更新流
        for stream in streams:
            stream_id = stream.get('stream_id')
            new_stream_ids.add(stream_id)
            
            if stream_id not in self.streams:
                # 新流
                self.add_stream(stream)
            else:
                # 更新流
                self.streams[stream_id] = stream
        
        # 移除不存在的流
        for stream_id in current_stream_ids - new_stream_ids:
            self.remove_stream(stream_id)
        
        # 更新状态标签
        self.status_label.setText(f"视频流: {len(self.streams)}")
    
    def add_stream(self, stream):
        """添加视频流"""
        stream_id = stream.get('stream_id')
        source_id = stream.get('source_id')
        stream_type = stream.get('stream_type')
        http_url = stream.get('http_url')
        
        # 存储流信息
        self.streams[stream_id] = stream
        
        # 创建视频流组件
        stream_widget = VideoStream(stream_id, source_id, stream_type, http_url, self)
        self.stream_widgets[stream_id] = stream_widget
        
        # 添加到布局
        self.update_layout()
        
        # 更新状态标签
        self.status_label.setText(f"视频流: {len(self.streams)}")
        
        # 通知服务器开始观看
        self.start_viewing(stream_id)
    
    def remove_stream(self, stream_id):
        """移除视频流"""
        if stream_id in self.streams:
            # 移除流信息
            del self.streams[stream_id]
            
            # 移除视频流组件
            if stream_id in self.stream_widgets:
                widget = self.stream_widgets[stream_id]
                widget.close()
                widget.deleteLater()
                del self.stream_widgets[stream_id]
            
            # 更新布局
            self.update_layout()
            
            # 更新状态标签
            self.status_label.setText(f"视频流: {len(self.streams)}")
            
            # 通知服务器停止观看
            self.stop_viewing(stream_id)
    
    def change_layout(self, index):
        """切换布局模式"""
        self.current_layout = self.layout_combo.currentData()
        self.update_layout()
    
    def update_layout(self):
        """更新布局"""
        # 清空布局
        while self.grid_layout.count():
            item = self.grid_layout.takeAt(0)
            if item.widget():
                item.widget().setParent(None)
        
        if self.current_layout == "grid":
            # 网格布局
            col_count = 2  # 每行2个视频
            row = 0
            col = 0
            
            for stream_id, widget in self.stream_widgets.items():
                self.grid_layout.addWidget(widget, row, col)
                
                col += 1
                if col >= col_count:
                    col = 0
                    row += 1
        
        elif self.current_layout == "single" and self.stream_widgets:
            # 单视图布局
            if self.current_stream and self.current_stream in self.stream_widgets:
                # 显示当前选中的流
                self.grid_layout.addWidget(self.stream_widgets[self.current_stream], 0, 0)
            else:
                # 显示第一个流
                stream_id = next(iter(self.stream_widgets))
                self.current_stream = stream_id
                self.grid_layout.addWidget(self.stream_widgets[stream_id], 0, 0)
    
    def stop_stream(self, stream_id):
        """停止流"""
        try:
            # 获取API基础URL
            api_base_url = self.parent.api_base_url
            
            # 发送请求停止流
            response = requests.post(f"{api_base_url}/video/streams/{stream_id}/stop")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.parent.status_bar.showMessage(f"流已停止: {stream_id}", 3000)
                else:
                    self.parent.show_error(f"停止流失败: {data.get('message')}")
            else:
                self.parent.show_error(f"停止流失败: HTTP {response.status_code}")
        
        except Exception as e:
            self.parent.show_error(f"停止流时出错: {str(e)}")
    
    def start_viewing(self, stream_id):
        """通知服务器开始观看流"""
        if self.socketio_thread:
            self.socketio_thread.emit('start_viewing', {
                'stream_id': stream_id,
                'viewer_id': self.parent.device_id
            })
    
    def stop_viewing(self, stream_id):
        """通知服务器停止观看流"""
        if self.socketio_thread:
            self.socketio_thread.emit('stop_viewing', {
                'stream_id': stream_id,
                'viewer_id': self.parent.device_id
            })
    
    def closeEvent(self, event):
        """关闭事件处理"""
        # 停止Socket.IO线程
        if self.socketio_thread and self.socketio_thread.isRunning():
            self.socketio_thread.stop()
        
        # 接受关闭事件
        event.accept()