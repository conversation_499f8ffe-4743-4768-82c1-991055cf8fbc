#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小组端连接测试工具
"""

import sys
import os
import logging
import requests
import socket
import time
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from group_client.config_loader import get_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('connection_test')

def test_http_connection():
    """测试HTTP连接"""
    config = get_config()
    url = f"http://{config.BACKEND_HOST}:{config.BACKEND_PORT}/api/health"
    
    logger.info(f"测试HTTP连接: {url}")
    
    try:
        response = requests.get(url, timeout=5)
        logger.info(f"HTTP响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            logger.info(f"HTTP连接成功: {response.json()}")
            return True
        else:
            logger.error(f"HTTP连接失败: {response.text}")
            return False
    except requests.exceptions.ConnectionError:
        logger.error(f"无法连接到服务器: {config.BACKEND_HOST}:{config.BACKEND_PORT}")
        return False
    except Exception as e:
        logger.error(f"HTTP连接测试出错: {str(e)}")
        return False

def test_classroom_api():
    """测试课堂API"""
    config = get_config()
    url = f"http://{config.BACKEND_HOST}:{config.BACKEND_PORT}/api/classroom/list"
    
    logger.info(f"测试课堂API: {url}")
    
    try:
        response = requests.get(url, timeout=5)
        logger.info(f"课堂API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                classrooms = data.get('classrooms', [])
                logger.info(f"获取到 {len(classrooms)} 个课堂")
                return True
            else:
                logger.error(f"课堂API返回错误: {data.get('error', '未知错误')}")
                return False
        else:
            logger.error(f"课堂API请求失败: {response.text}")
            return False
    except Exception as e:
        logger.error(f"课堂API测试出错: {str(e)}")
        return False

def test_groups_api():
    """测试小组API（使用测试课堂ID）"""
    config = get_config()
    # 使用一个测试课堂ID
    test_classroom_id = "test_classroom_1"
    url = f"http://{config.BACKEND_HOST}:{config.BACKEND_PORT}/api/groups"
    
    logger.info(f"测试小组API: {url}?classroom_id={test_classroom_id}")
    
    try:
        response = requests.get(url, params={"classroom_id": test_classroom_id}, timeout=5)
        logger.info(f"小组API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                groups = data.get('groups', [])
                logger.info(f"获取到 {len(groups)} 个小组")
                return True
            else:
                logger.error(f"小组API返回错误: {data.get('message', '未知错误')}")
                return False
        elif response.status_code == 400:
            # 400错误可能是因为测试课堂ID不存在，这是预期的
            logger.info("小组API返回400错误，这可能是因为测试课堂ID不存在")
            return True
        else:
            logger.error(f"小组API请求失败: {response.text}")
            return False
    except Exception as e:
        logger.error(f"小组API测试出错: {str(e)}")
        return False

def test_socket_connection():
    """测试套接字连接"""
    config = get_config()
    host = config.BACKEND_HOST
    port = config.BACKEND_PORT
    
    logger.info(f"测试套接字连接: {host}:{port}")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            logger.info(f"套接字连接成功")
            return True
        else:
            logger.error(f"套接字连接失败，错误码: {result}")
            return False
    except Exception as e:
        logger.error(f"套接字连接测试出错: {str(e)}")
        return False

def run_tests():
    """运行所有测试"""
    config = get_config()
    logger.info(f"使用配置: BACKEND_HOST={config.BACKEND_HOST}, BACKEND_PORT={config.BACKEND_PORT}")
    
    socket_result = test_socket_connection()
    time.sleep(1)
    http_result = test_http_connection()
    time.sleep(1)
    classroom_result = test_classroom_api()
    time.sleep(1)
    groups_result = test_groups_api()
    
    logger.info("测试结果摘要:")
    logger.info(f"套接字连接: {'成功' if socket_result else '失败'}")
    logger.info(f"HTTP连接: {'成功' if http_result else '失败'}")
    logger.info(f"课堂API: {'成功' if classroom_result else '失败'}")
    logger.info(f"小组API: {'成功' if groups_result else '失败'}")
    
    if socket_result and http_result and classroom_result and groups_result:
        logger.info("所有测试通过，服务器连接正常")
    elif not socket_result:
        logger.info("无法连接到服务器，请检查网络连接和服务器状态")
    elif not http_result:
        logger.info("服务器端口可访问，但HTTP服务可能有问题")
    elif not classroom_result:
        logger.info("HTTP服务正常，但课堂API可能有问题")
    elif not groups_result:
        logger.info("课堂API正常，但小组API可能有问题")

if __name__ == '__main__':
    run_tests()