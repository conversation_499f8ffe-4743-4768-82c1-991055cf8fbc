#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小组端配置加载器
"""

import os
import sys
import logging
import importlib.util
from shared.config import get_config as get_shared_config

logger = logging.getLogger('config_loader')

def get_config():
    """
    获取小组端配置
    优先使用本地配置，如果不存在则使用共享配置
    """
    # 首先获取共享配置
    config = get_shared_config()
    
    # 尝试加载本地配置
    try:
        # 检查本地配置文件是否存在
        local_config_path = os.path.join(os.path.dirname(__file__), 'local_config.py')
        if os.path.exists(local_config_path):
            # 动态加载本地配置模块
            spec = importlib.util.spec_from_file_location('local_config', local_config_path)
            local_config_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(local_config_module)
            
            # 使用本地配置覆盖共享配置
            if hasattr(local_config_module, 'LocalConfig'):
                logger.info("已加载本地配置")
                return local_config_module.LocalConfig()
    except Exception as e:
        logger.warning(f"加载本地配置失败: {str(e)}")
    
    # 如果没有本地配置或加载失败，使用共享配置
    logger.info("使用共享配置")
    return config