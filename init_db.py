#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""

import os
import sys
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# 添加项目路径到 Python 路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    
    # 确保 instance 目录存在
    instance_path = os.path.join(project_root, 'instance')
    if not os.path.exists(instance_path):
        os.makedirs(instance_path)
    
    # 配置数据库
    db_path = os.path.join(instance_path, 'smart_classroom.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = 'smart_classroom_secret_key'
    
    return app

def init_database():
    """初始化数据库"""
    app = create_app()
    
    # 导入所有模型
    from backend.models import db
    from backend.models.classroom import Classroom, ClassroomActivity
    from backend.models.device import Device, DeviceLog
    from backend.models.group import Group, GroupScore
    from backend.models.question import Question, Answer
    from backend.models.student import Student, StudentActivity
    from backend.models.whiteboard import Whiteboard, WhiteboardVersion
    from backend.models.display import DisplayConfig, GroupTimer, DiscussionTopic, DisplayLog
    from backend.models.report import ClassroomReport, ReportTemplate, LearningAnalytics, DataExport
    from backend.models.security import (
        User, UserSession, UserPermission, DeviceAccess, 
        AuditLog, SecurityConfig, DataEncryption,
        UserRole, Permission
    )
    
    # 初始化数据库
    db.init_app(app)
    
    with app.app_context():
        print("正在创建数据库表...")
        db.create_all()
        print("数据库表创建完成！")
        
        # 创建默认数据
        create_default_data()
        
        print("数据库初始化完成！")

def create_default_data():
    """创建默认数据"""
    from backend.models import db
    from backend.models.security import User, UserRole, Permission
    
    try:
        # 创建默认角色
        admin_role = UserRole.query.filter_by(name='admin').first()
        if not admin_role:
            admin_role = UserRole(name='admin', description='系统管理员')
            db.session.add(admin_role)
        
        teacher_role = UserRole.query.filter_by(name='teacher').first()
        if not teacher_role:
            teacher_role = UserRole(name='teacher', description='教师')
            db.session.add(teacher_role)
        
        student_role = UserRole.query.filter_by(name='student').first()
        if not student_role:
            student_role = UserRole(name='student', description='学生')
            db.session.add(student_role)
        
        # 创建默认管理员用户
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                role_id=admin_role.id
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
        
        db.session.commit()
        print("默认数据创建完成！")
        print("默认管理员账号: admin / admin123")
        
    except Exception as e:
        print(f"创建默认数据时出错: {e}")
        db.session.rollback()

if __name__ == '__main__':
    init_database()