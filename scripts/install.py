#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统安装程序
Smart Classroom System Installer
"""

import os
import sys
import subprocess
import shutil
import platform
import json
import yaml
from pathlib import Path
from typing import Dict, List, Optional
import argparse


class SmartClassroomInstaller:
    """智慧课堂系统安装器"""
    
    def __init__(self):
        self.script_dir = Path(__file__).parent
        self.project_root = self.script_dir.parent
        self.install_dir = Path("/opt/smart-classroom")
        self.service_user = "smartclass"
        self.log_dir = Path("/var/log/smart-classroom")
        self.data_dir = Path("/var/lib/smart-classroom")
        
        # 安装选项
        self.install_options = {
            'install_teacher_client': True,
            'install_group_client': True,
            'install_backend': True,
            'install_web_client': True,
            'install_mediamtx': True,
            'configure_nginx': True,
            'create_services': True,
            'initialize_database': True
        }
    
    def print_banner(self):
        """打印安装横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    智慧课堂系统安装程序                        ║
║                Smart Classroom System Installer              ║
║                                                              ║
║  版本: 1.0.0                                                 ║
║  支持系统: 统信UOS 20+, Ubuntu 20.04+                        ║
║  Python要求: 3.8+                                            ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def check_system_requirements(self) -> bool:
        """检查系统要求"""
        print("🔍 检查系统要求...")
        
        # 检查操作系统
        system = platform.system()
        if system != "Linux":
            print(f"❌ 不支持的操作系统: {system}")
            return False
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version < (3, 7):
            print(f"❌ Python过低: {python_version.major}.{python_version.minor}")
            print("   需要Python 3.7或更高版本")
            return False
        
        # 检查权限
        if os.geteuid() != 0:
            print("❌ 需要root权限运行安装程序")
            return False
        
        # 检查磁盘空间
        disk_usage = shutil.disk_usage("/")
        free_gb = disk_usage.free // (1024**3)
        if free_gb < 10:
            print(f"⚠️  磁盘空间不足: {free_gb}GB (建议至少10GB)")
        
        print("✅ 系统要求检查通过")
        return True
    
    def install_system_dependencies(self) -> bool:
        """安装系统依赖"""
        print("📦 安装系统依赖...")
        
        try:
            # 更新包管理器
            subprocess.run(["apt", "update"], check=True, capture_output=True)
            
            # 基础依赖包
            packages = [
                "python3-pip", "python3-venv", "python3-dev",
                "build-essential", "pkg-config",
                "libgl1-mesa-glx", "libglib2.0-0",
                "libxcb-xinerama0", "libxcb-cursor0", "libxkbcommon-x11-0",
                "x11-utils", "xdotool", "ffmpeg", "sqlite3",
                "nginx", "supervisor", "curl", "wget"
            ]
            
            subprocess.run(["apt", "install", "-y"] + packages, 
                         check=True, capture_output=True)
            
            print("✅ 系统依赖安装完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 系统依赖安装失败: {e}")
            return False
    
    def install_mediamtx(self) -> bool:
        """安装MediaMTX流媒体服务器"""
        print("🎥 安装MediaMTX流媒体服务器...")
        
        try:
            # 检查是否已安装
            if shutil.which("mediamtx"):
                print("✅ MediaMTX已安装")
                return True
            
            # 下载并安装MediaMTX
            download_url = "https://github.com/bluenviron/mediamtx/releases/latest/download/mediamtx_v1.0.0_linux_amd64.tar.gz"
            temp_file = "/tmp/mediamtx.tar.gz"
            
            subprocess.run(["wget", "-O", temp_file, download_url], 
                         check=True, capture_output=True)
            subprocess.run(["tar", "-xzf", temp_file, "-C", "/usr/local/bin/"], 
                         check=True, capture_output=True)
            subprocess.run(["chmod", "+x", "/usr/local/bin/mediamtx"], 
                         check=True, capture_output=True)
            
            os.remove(temp_file)
            print("✅ MediaMTX安装完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ MediaMTX安装失败: {e}")
            return False
    
    def create_user_and_directories(self) -> bool:
        """创建用户和目录"""
        print("👤 创建用户和目录...")
        
        try:
            # 创建系统用户
            try:
                subprocess.run(["id", self.service_user], 
                             check=True, capture_output=True)
                print(f"✅ 用户 {self.service_user} 已存在")
            except subprocess.CalledProcessError:
                subprocess.run([
                    "useradd", "-r", "-s", "/bin/false", 
                    "-d", str(self.data_dir), self.service_user
                ], check=True, capture_output=True)
                print(f"✅ 创建用户: {self.service_user}")
            
            # 创建目录
            for directory in [self.install_dir, self.log_dir, self.data_dir]:
                directory.mkdir(parents=True, exist_ok=True)
                shutil.chown(directory, self.service_user, self.service_user)
            
            print("✅ 用户和目录创建完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 用户和目录创建失败: {e}")
            return False
    
    def install_python_environment(self) -> bool:
        """安装Python环境"""
        print("🐍 安装Python环境...")
        
        try:
            venv_dir = self.install_dir / "venv"
            
            # 创建虚拟环境
            subprocess.run([
                sys.executable, "-m", "venv", str(venv_dir)
            ], check=True, capture_output=True)
            
            # 激活虚拟环境并安装依赖
            pip_path = venv_dir / "bin" / "pip"
            python_path = venv_dir / "bin" / "python"
            
            # 升级pip
            subprocess.run([str(pip_path), "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            
            # 安装项目依赖
            requirements_file = self.project_root / "requirements.txt"
            subprocess.run([str(pip_path), "install", "-r", str(requirements_file)], 
                         check=True, capture_output=True)
            
            # 安装项目本身
            subprocess.run([str(pip_path), "install", "-e", str(self.project_root)], 
                         check=True, capture_output=True)
            
            print("✅ Python环境安装完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Python环境安装失败: {e}")
            return False
    
    def copy_project_files(self) -> bool:
        """复制项目文件"""
        print("📁 复制项目文件...")
        
        try:
            # 复制源代码
            for item in self.project_root.iterdir():
                if item.name in ['.git', '__pycache__', '.pytest_cache', 'venv']:
                    continue
                
                dest = self.install_dir / item.name
                if item.is_dir():
                    if dest.exists():
                        shutil.rmtree(dest)
                    shutil.copytree(item, dest)
                else:
                    shutil.copy2(item, dest)
            
            # 设置权限
            shutil.chown(self.install_dir, self.service_user, self.service_user)
            for root, dirs, files in os.walk(self.install_dir):
                for d in dirs:
                    shutil.chown(os.path.join(root, d), self.service_user, self.service_user)
                for f in files:
                    shutil.chown(os.path.join(root, f), self.service_user, self.service_user)
            
            # 设置脚本执行权限
            scripts_dir = self.install_dir / "scripts"
            if scripts_dir.exists():
                for script in scripts_dir.glob("*.sh"):
                    script.chmod(0o755)
            
            print("✅ 项目文件复制完成")
            return True
            
        except Exception as e:
            print(f"❌ 项目文件复制失败: {e}")
            return False
    
    def create_configuration_files(self) -> bool:
        """创建配置文件"""
        print("⚙️  创建配置文件...")
        
        try:
            config_dir = self.install_dir / "config"
            config_dir.mkdir(exist_ok=True)
            
            # MediaMTX配置
            mediamtx_config = {
                'logLevel': 'info',
                'logDestinations': ['stdout'],
                'logFile': str(self.log_dir / 'mediamtx.log'),
                'api': True,
                'apiAddress': '127.0.0.1:9997',
                'rtmp': True,
                'rtmpAddress': ':1935',
                'webrtc': True,
                'webrtcAddress': ':8889',
                'paths': {
                    'all': {
                        'source': 'publisher',
                        'publishUser': '',
                        'publishPass': '',
                        'readUser': '',
                        'readPass': ''
                    }
                }
            }
            
            with open(config_dir / "mediamtx.yml", 'w', encoding='utf-8') as f:
                yaml.dump(mediamtx_config, f, default_flow_style=False, allow_unicode=True)
            
            # 应用配置
            app_config = {
                'app': {
                    'name': '智慧课堂系统',
                    'version': '1.0.0',
                    'debug': False,
                    'host': '0.0.0.0',
                    'port': 5000
                },
                'database': {
                    'url': f'sqlite:///{self.data_dir}/smart_classroom.db',
                    'echo': False
                },
                'logging': {
                    'level': 'INFO',
                    'file': str(self.log_dir / 'app.log'),
                    'max_bytes': 10485760,
                    'backup_count': 5
                },
                'mediamtx': {
                    'rtmp_url': 'rtmp://localhost:1935',
                    'api_url': 'http://localhost:9997'
                },
                'security': {
                    'secret_key': os.urandom(32).hex(),
                    'jwt_secret': os.urandom(32).hex(),
                    'password_salt': os.urandom(16).hex()
                }
            }
            
            with open(config_dir / "production.yml", 'w', encoding='utf-8') as f:
                yaml.dump(app_config, f, default_flow_style=False, allow_unicode=True)
            
            # 设置权限
            shutil.chown(config_dir, self.service_user, self.service_user)
            for config_file in config_dir.iterdir():
                shutil.chown(config_file, self.service_user, self.service_user)
            
            print("✅ 配置文件创建完成")
            return True
            
        except Exception as e:
            print(f"❌ 配置文件创建失败: {e}")
            return False
    
    def create_systemd_services(self) -> bool:
        """创建systemd服务"""
        print("🔧 创建系统服务...")
        
        try:
            # 后端服务
            backend_service = f"""[Unit]
Description=Smart Classroom Backend Service
After=network.target

[Service]
Type=simple
User={self.service_user}
WorkingDirectory={self.install_dir}
Environment=PATH={self.install_dir}/venv/bin
ExecStart={self.install_dir}/venv/bin/python -m backend.app
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
"""
            
            with open("/etc/systemd/system/smart-classroom-backend.service", 'w') as f:
                f.write(backend_service)
            
            # MediaMTX服务
            mediamtx_service = f"""[Unit]
Description=MediaMTX Streaming Server
After=network.target

[Service]
Type=simple
User={self.service_user}
ExecStart=/usr/local/bin/mediamtx {self.install_dir}/config/mediamtx.yml
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
"""
            
            with open("/etc/systemd/system/mediamtx.service", 'w') as f:
                f.write(mediamtx_service)
            
            # 重新加载systemd
            subprocess.run(["systemctl", "daemon-reload"], check=True)
            
            # 启用服务
            subprocess.run(["systemctl", "enable", "smart-classroom-backend"], check=True)
            subprocess.run(["systemctl", "enable", "mediamtx"], check=True)
            
            print("✅ 系统服务创建完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 系统服务创建失败: {e}")
            return False
    
    def configure_nginx(self) -> bool:
        """配置Nginx"""
        print("🌐 配置Nginx...")
        
        try:
            nginx_config = f"""server {{
    listen 80;
    server_name _;
    
    # 静态文件
    location /static/ {{
        alias {self.install_dir}/web_client/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }}
    
    # API代理
    location /api/ {{
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }}
    
    # WebSocket代理
    location /socket.io/ {{
        proxy_pass http://127.0.0.1:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }}
    
    # 默认页面
    location / {{
        root {self.install_dir}/web_client;
        index index.html;
        try_files $uri $uri/ /index.html;
    }}
}}
"""
            
            with open("/etc/nginx/sites-available/smart-classroom", 'w') as f:
                f.write(nginx_config)
            
            # 启用站点
            sites_enabled = Path("/etc/nginx/sites-enabled")
            sites_enabled.mkdir(exist_ok=True)
            
            smart_classroom_link = sites_enabled / "smart-classroom"
            if smart_classroom_link.exists():
                smart_classroom_link.unlink()
            smart_classroom_link.symlink_to("/etc/nginx/sites-available/smart-classroom")
            
            # 删除默认站点
            default_link = sites_enabled / "default"
            if default_link.exists():
                default_link.unlink()
            
            # 测试配置
            subprocess.run(["nginx", "-t"], check=True, capture_output=True)
            
            # 启用并重启Nginx
            subprocess.run(["systemctl", "enable", "nginx"], check=True)
            subprocess.run(["systemctl", "restart", "nginx"], check=True)
            
            print("✅ Nginx配置完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Nginx配置失败: {e}")
            return False
    
    def initialize_database(self) -> bool:
        """初始化数据库"""
        print("🗄️  初始化数据库...")
        
        try:
            python_path = self.install_dir / "venv" / "bin" / "python"
            init_script = self.install_dir / "init_db.py"
            
            subprocess.run([
                "sudo", "-u", self.service_user,
                str(python_path), str(init_script)
            ], cwd=str(self.install_dir), check=True, capture_output=True)
            
            print("✅ 数据库初始化完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 数据库初始化失败: {e}")
            return False
    
    def start_services(self) -> bool:
        """启动服务"""
        print("🚀 启动服务...")
        
        try:
            services = ["mediamtx", "smart-classroom-backend", "nginx"]
            
            for service in services:
                subprocess.run(["systemctl", "start", service], check=True)
                
                # 检查服务状态
                result = subprocess.run(
                    ["systemctl", "is-active", service], 
                    capture_output=True, text=True
                )
                
                if result.stdout.strip() == "active":
                    print(f"✅ {service} 服务启动成功")
                else:
                    print(f"❌ {service} 服务启动失败")
                    return False
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 服务启动失败: {e}")
            return False
    
    def create_desktop_shortcuts(self) -> bool:
        """创建桌面快捷方式"""
        print("🖥️  创建桌面快捷方式...")
        
        try:
            applications_dir = Path("/usr/share/applications")
            applications_dir.mkdir(exist_ok=True)
            
            # 教师端快捷方式
            teacher_desktop = f"""[Desktop Entry]
Version=1.0
Type=Application
Name=智慧课堂-教师端
Name[en]=Smart Classroom Teacher
Comment=智慧课堂系统教师端应用
Comment[en]=Smart Classroom System Teacher Application
Exec={self.install_dir}/venv/bin/python {self.install_dir}/teacher_client/main.py
Icon={self.install_dir}/web_client/images/teacher-icon.png
Terminal=false
Categories=Education;
"""
            
            with open(applications_dir / "smart-classroom-teacher.desktop", 'w') as f:
                f.write(teacher_desktop)
            
            # 小组端快捷方式
            group_desktop = f"""[Desktop Entry]
Version=1.0
Type=Application
Name=智慧课堂-小组端
Name[en]=Smart Classroom Group
Comment=智慧课堂系统小组端应用
Comment[en]=Smart Classroom System Group Application
Exec={self.install_dir}/venv/bin/python {self.install_dir}/group_client/main.py
Icon={self.install_dir}/web_client/images/group-icon.png
Terminal=false
Categories=Education;
"""
            
            with open(applications_dir / "smart-classroom-group.desktop", 'w') as f:
                f.write(group_desktop)
            
            print("✅ 桌面快捷方式创建完成")
            return True
            
        except Exception as e:
            print(f"❌ 桌面快捷方式创建失败: {e}")
            return False
    
    def run_installation(self) -> bool:
        """运行完整安装流程"""
        self.print_banner()
        
        steps = [
            ("检查系统要求", self.check_system_requirements),
            ("安装系统依赖", self.install_system_dependencies),
            ("安装MediaMTX", self.install_mediamtx),
            ("创建用户和目录", self.create_user_and_directories),
            ("安装Python环境", self.install_python_environment),
            ("复制项目文件", self.copy_project_files),
            ("创建配置文件", self.create_configuration_files),
            ("创建系统服务", self.create_systemd_services),
            ("配置Nginx", self.configure_nginx),
            ("初始化数据库", self.initialize_database),
            ("启动服务", self.start_services),
            ("创建桌面快捷方式", self.create_desktop_shortcuts),
        ]
        
        for step_name, step_func in steps:
            print(f"\n{'='*60}")
            print(f"执行步骤: {step_name}")
            print('='*60)
            
            if not step_func():
                print(f"\n❌ 安装失败于步骤: {step_name}")
                return False
        
        self.print_success_message()
        return True
    
    def print_success_message(self):
        """打印安装成功信息"""
        import socket
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        
        success_message = f"""
╔══════════════════════════════════════════════════════════════╗
║                    🎉 安装成功完成！                          ║
╚══════════════════════════════════════════════════════════════╝

📍 访问地址:
   学生端Web界面: http://{local_ip}
   系统管理界面: http://{local_ip}/admin

🔧 服务管理命令:
   查看服务状态: systemctl status smart-classroom-backend
   重启后端服务: systemctl restart smart-classroom-backend
   查看日志: journalctl -u smart-classroom-backend -f

🖥️  客户端启动:
   教师端: {self.install_dir}/venv/bin/python {self.install_dir}/teacher_client/main.py
   小组端: {self.install_dir}/venv/bin/python {self.install_dir}/group_client/main.py

📁 重要目录:
   安装目录: {self.install_dir}
   日志目录: {self.log_dir}
   数据目录: {self.data_dir}

🆘 获取帮助:
   查看文档: {self.install_dir}/docs/
   技术支持: 请联系系统管理员
        """
        print(success_message)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="智慧课堂系统安装程序")
    parser.add_argument("--dry-run", action="store_true", help="仅检查系统要求，不执行安装")
    parser.add_argument("--config-only", action="store_true", help="仅创建配置文件")
    
    args = parser.parse_args()
    
    installer = SmartClassroomInstaller()
    
    if args.dry_run:
        installer.print_banner()
        installer.check_system_requirements()
        return
    
    if args.config_only:
        installer.create_configuration_files()
        return
    
    # 执行完整安装
    success = installer.run_installation()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()