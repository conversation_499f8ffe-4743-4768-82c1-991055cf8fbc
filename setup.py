#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧课堂系统安装配置
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="smart-classroom-system",
    version="1.0.0",
    author="Smart Classroom Team",
    author_email="<EMAIL>",
    description="基于统信UOS的多端协作教学平台",
    long_description=long_description,
    long_description_content_type="text/markdown",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Education",
        "Operating System :: POSIX :: Linux",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: Education",
        "Topic :: Multimedia :: Video",
    ],
    python_requires=">=3.7.3",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "smart-classroom-teacher=teacher_client.main:main",
            "smart-classroom-group=group_client.main:main",
            "smart-classroom-backend=backend.app:main",
        ],
    },
    include_package_data=True,
    package_data={
        "web_client": ["*.html", "css/*.css", "js/*.js"],
        "config": ["*.yml", "*.json"],
    },
)